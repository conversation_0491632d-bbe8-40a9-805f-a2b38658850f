/**
 * NFC功能测试脚本
 * 用于验证NFC工具类的基本功能
 */

// 模拟uni对象用于测试
const mockUni = {
  getNFCAdapter: () => ({
    onAdapterStateChange: (callback) => {
      console.log('监听NFC适配器状态变化');
      // 模拟状态变化
      setTimeout(() => {
        callback({ available: true, discovering: false });
      }, 100);
    },
    getAdapterState: () => Promise.resolve({
      available: true,
      discovering: false
    }),
    startDiscovery: () => {
      console.log('开始NFC发现');
      return Promise.resolve();
    },
    stopDiscovery: () => {
      console.log('停止NFC发现');
      return Promise.resolve();
    },
    onTagFound: (callback) => {
      console.log('监听NFC标签发现');
      // 模拟标签发现
      setTimeout(() => {
        callback({
          tagId: 'test-tag-123',
          techs: ['NDEF', 'NfcA'],
          messages: [{
            records: [{
              id: new ArrayBuffer(0),
              type: new TextEncoder().encode('T').buffer,
              payload: new TextEncoder().encode('Hello NFC!').buffer
            }]
          }]
        });
      }, 2000);
    },
    getNdef: () => ({
      readNdefMessage: () => Promise.resolve({
        messages: [{
          records: [{
            id: new ArrayBuffer(0),
            type: new TextEncoder().encode('T').buffer,
            payload: new TextEncoder().encode('Test NFC Data').buffer
          }]
        }]
      }),
      writeNdefMessage: (message) => {
        console.log('写入NDEF消息:', message);
        return Promise.resolve();
      }
    })
  }),
  $emit: (event, data) => {
    console.log(`触发事件: ${event}`, data);
  },
  $on: (event, callback) => {
    console.log(`监听事件: ${event}`);
  },
  $off: (event, callback) => {
    console.log(`取消监听事件: ${event}`);
  }
};

// 在Node.js环境中模拟全局uni对象
if (typeof global !== 'undefined') {
  global.uni = mockUni;
  global.TextEncoder = require('util').TextEncoder;
  global.TextDecoder = require('util').TextDecoder;
}

// 测试函数
async function testNFCManager() {
  console.log('=== NFC功能测试开始 ===\n');

  try {
    // 动态导入NFC管理器（适配不同环境）
    let nfcManager;
    try {
      // 尝试ES6模块导入
      const module = await import('../utils/nfc.js');
      nfcManager = module.default;
    } catch (e) {
      // 回退到CommonJS
      nfcManager = require('../utils/nfc.js');
    }

    // 测试1: 检查NFC可用性
    console.log('1. 测试NFC可用性检查...');
    const availability = await nfcManager.checkNFCAvailability();
    console.log('NFC可用性:', availability);
    console.log('✅ NFC可用性检查完成\n');

    // 测试2: 开始NFC发现
    console.log('2. 测试NFC发现功能...');
    await nfcManager.startDiscovery();
    console.log('✅ NFC发现启动成功\n');

    // 等待模拟标签发现
    await new Promise(resolve => setTimeout(resolve, 2500));

    // 测试3: 读取NDEF数据
    console.log('3. 测试NDEF数据读取...');
    try {
      const ndefData = await nfcManager.readNDEF('test-tag-123');
      console.log('读取到的NDEF数据:', ndefData);
      console.log('✅ NDEF数据读取成功\n');
    } catch (error) {
      console.log('❌ NDEF数据读取失败:', error.message, '\n');
    }

    // 测试4: 写入NDEF数据
    console.log('4. 测试NDEF数据写入...');
    try {
      const writeResult = await nfcManager.writeNDEF('测试写入数据', 'test-tag-123');
      console.log('写入结果:', writeResult);
      console.log('✅ NDEF数据写入成功\n');
    } catch (error) {
      console.log('❌ NDEF数据写入失败:', error.message, '\n');
    }

    // 测试5: 获取操作记录
    console.log('5. 测试操作记录管理...');
    const records = nfcManager.getRecords(5);
    console.log(`获取到 ${records.length} 条记录:`);
    records.forEach((record, index) => {
      console.log(`  记录 ${index + 1}:`, {
        id: record.id,
        type: record.type,
        timestamp: record.timestamp,
        success: record.success
      });
    });
    console.log('✅ 操作记录获取成功\n');

    // 测试6: 数据格式化
    console.log('6. 测试数据格式化...');
    const testData = {
      records: [{
        payload: new TextEncoder().encode('格式化测试数据').buffer
      }]
    };
    const formatted = nfcManager.formatNFCData(testData);
    console.log('格式化结果:', formatted);
    console.log('✅ 数据格式化成功\n');

    // 测试7: 导出记录
    console.log('7. 测试记录导出...');
    const exportData = nfcManager.exportRecords();
    console.log('导出数据长度:', exportData.length, '字符');
    console.log('✅ 记录导出成功\n');

    // 停止NFC发现
    await nfcManager.stopDiscovery();
    console.log('✅ NFC发现已停止\n');

    console.log('=== 所有测试完成 ===');
    console.log('🎉 NFC功能测试全部通过！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  module.exports = { testNFCManager };
  
  // 如果直接运行此文件
  if (require.main === module) {
    testNFCManager();
  }
} else {
  // 浏览器环境
  window.testNFCManager = testNFCManager;
}

// 导出测试函数供其他地方调用
export { testNFCManager };

{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "NFC管理工具"
			}
		},
		{
			"path": "pages/nfc-read/nfc-read",
			"style": {
				"navigationBarTitleText": "NFC读取",
				"navigationBarBackgroundColor": "#667eea",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/nfc-write/nfc-write",
			"style": {
				"navigationBarTitleText": "NFC写入",
				"navigationBarBackgroundColor": "#f093fb",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/nfc-records/nfc-records",
			"style": {
				"navigationBarTitleText": "操作记录",
				"navigationBarBackgroundColor": "#4facfe",
				"navigationBarTextStyle": "white"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "NFC管理工具",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"enablePullDownRefresh": false
	},
	"uniIdRouter": {}
}

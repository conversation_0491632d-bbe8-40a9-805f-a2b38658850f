/**
 * NFC工具类
 * 封装NFC的读取、写入、记录等核心功能
 */
class NFCManager {
  constructor() {
    this.nfcAdapter = null;
    this.isNFCAvailable = false;
    this.isDiscovering = false;
    this.records = []; // 存储NFC操作记录
    this.init();
  }

  /**
   * 初始化NFC适配器
   */
  async init() {
    try {
      // 检查是否支持NFC
      if (!uni.getNFCAdapter) {
        throw new Error('当前环境不支持NFC功能');
      }

      // 获取NFC适配器
      this.nfcAdapter = uni.getNFCAdapter();

      // 监听NFC适配器状态变化
      this.nfcAdapter.onAdapterStateChange((res) => {
        console.log('NFC适配器状态变化:', res);
        this.isNFCAvailable = res.available;
        this.isDiscovering = res.discovering;
      });

      // 监听NFC标签发现
      this.nfcAdapter.onTagFound((tag) => {
        console.log('发现NFC标签:', tag);
        this.handleTagFound(tag);
      });

      // 获取初始状态
      try {
        const state = await this.nfcAdapter.getAdapterState();
        this.isNFCAvailable = state.available;
        this.isDiscovering = state.discovering;
        console.log('NFC适配器初始化成功:', state);
      } catch (stateError) {
        console.warn('获取NFC状态失败，使用默认值:', stateError);
        // 在某些情况下可能无法获取状态，设置默认值
        this.isNFCAvailable = true;
        this.isDiscovering = false;
      }

    } catch (error) {
      console.error('NFC适配器初始化失败:', error);
      this.isNFCAvailable = false;
    }
  }

  /**
   * 检查NFC是否可用
   */
  async checkNFCAvailability() {
    if (!this.nfcAdapter) {
      return { available: false, message: 'NFC适配器未初始化' };
    }

    try {
      const state = await this.nfcAdapter.getAdapterState();
      return {
        available: state.available,
        discovering: state.discovering,
        message: state.available ? 'NFC可用' : 'NFC不可用，请检查设备设置'
      };
    } catch (error) {
      return { available: false, message: '检查NFC状态失败: ' + error.message };
    }
  }

  /**
   * 开始NFC发现
   */
  async startDiscovery() {
    if (!this.nfcAdapter) {
      throw new Error('NFC适配器未初始化');
    }

    try {
      // 检查NFC状态
      const status = await this.checkNFCAvailability();
      if (!status.available) {
        throw new Error(status.message);
      }

      await this.nfcAdapter.startDiscovery({
        success: () => {
          this.isDiscovering = true;
          console.log('开始NFC发现成功');
        },
        fail: (error) => {
          console.error('启动NFC发现失败:', error);
          throw new Error('启动NFC发现失败: ' + (error.errMsg || error.message));
        }
      });

    } catch (error) {
      console.error('启动NFC发现失败:', error);
      throw error;
    }
  }

  /**
   * 停止NFC发现
   */
  async stopDiscovery() {
    if (!this.nfcAdapter) {
      return;
    }

    try {
      await this.nfcAdapter.stopDiscovery({
        success: () => {
          this.isDiscovering = false;
          console.log('停止NFC发现成功');
        },
        fail: (error) => {
          console.error('停止NFC发现失败:', error);
        }
      });
    } catch (error) {
      console.error('停止NFC发现失败:', error);
    }
  }

  /**
   * 处理发现的NFC标签
   */
  handleTagFound(tagData) {
    const record = {
      id: Date.now().toString(),
      type: 'read',
      timestamp: new Date(),
      tagId: tagData.tagId,
      techs: tagData.techs,
      messages: tagData.messages || [],
      rawData: tagData
    };

    this.records.unshift(record);
    
    // 触发自定义事件
    uni.$emit('nfc-tag-found', record);
  }

  /**
   * 读取NDEF数据
   */
  async readNDEF(tagId) {
    if (!this.nfcAdapter) {
      throw new Error('NFC适配器未初始化');
    }

    try {
      const ndef = this.nfcAdapter.getNdef();

      return new Promise((resolve, reject) => {
        ndef.readNdefMessage({
          success: (result) => {
            const record = {
              id: Date.now().toString(),
              type: 'read',
              timestamp: new Date(),
              tagId: tagId,
              messages: result.messages,
              success: true
            };

            this.records.unshift(record);
            resolve(result);
          },
          fail: (error) => {
            const record = {
              id: Date.now().toString(),
              type: 'read',
              timestamp: new Date(),
              tagId: tagId,
              error: error.errMsg || error.message,
              success: false
            };

            this.records.unshift(record);
            reject(new Error(error.errMsg || error.message || '读取NDEF失败'));
          }
        });
      });
    } catch (error) {
      const record = {
        id: Date.now().toString(),
        type: 'read',
        timestamp: new Date(),
        tagId: tagId,
        error: error.message,
        success: false
      };

      this.records.unshift(record);
      throw error;
    }
  }

  /**
   * 写入NDEF数据
   */
  async writeNDEF(data, tagId) {
    if (!this.nfcAdapter) {
      throw new Error('NFC适配器未初始化');
    }

    try {
      const ndef = this.nfcAdapter.getNdef();

      // 构造NDEF消息
      const message = {
        records: [{
          id: new ArrayBuffer(0),
          type: new TextEncoder().encode('T').buffer,
          payload: new TextEncoder().encode(data).buffer
        }]
      };

      return new Promise((resolve, reject) => {
        ndef.writeNdefMessage({
          messages: [message],
          success: () => {
            const record = {
              id: Date.now().toString(),
              type: 'write',
              timestamp: new Date(),
              tagId: tagId,
              data: data,
              success: true
            };

            this.records.unshift(record);
            resolve({ success: true, message: '写入成功' });
          },
          fail: (error) => {
            const record = {
              id: Date.now().toString(),
              type: 'write',
              timestamp: new Date(),
              tagId: tagId,
              data: data,
              error: error.errMsg || error.message,
              success: false
            };

            this.records.unshift(record);
            reject(new Error(error.errMsg || error.message || '写入NDEF失败'));
          }
        });
      });
    } catch (error) {
      const record = {
        id: Date.now().toString(),
        type: 'write',
        timestamp: new Date(),
        tagId: tagId,
        data: data,
        error: error.message,
        success: false
      };

      this.records.unshift(record);
      throw error;
    }
  }

  /**
   * 获取操作记录
   */
  getRecords(limit = 50) {
    return this.records.slice(0, limit);
  }

  /**
   * 清除记录
   */
  clearRecords() {
    this.records = [];
  }

  /**
   * 删除指定记录
   */
  deleteRecord(recordId) {
    const index = this.records.findIndex(record => record.id === recordId);
    if (index > -1) {
      this.records.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 导出记录为JSON
   */
  exportRecords() {
    return JSON.stringify(this.records, null, 2);
  }

  /**
   * 格式化NFC数据显示
   */
  formatNFCData(data) {
    if (!data) return '无数据';

    if (typeof data === 'string') {
      return data;
    }

    // 处理微信小程序NDEF消息格式
    if (Array.isArray(data)) {
      // data是messages数组
      return data.map(message => {
        if (message.records && Array.isArray(message.records)) {
          return message.records.map(record => {
            if (record.payload) {
              try {
                // 微信小程序中payload是ArrayBuffer
                const decoder = new TextDecoder('utf-8');
                return decoder.decode(record.payload);
              } catch (e) {
                return '二进制数据';
              }
            }
            return '未知格式';
          }).join('\n');
        }
        return '无记录';
      }).join('\n---\n');
    }

    // 处理单个message对象
    if (data.records && Array.isArray(data.records)) {
      return data.records.map(record => {
        if (record.payload) {
          try {
            const decoder = new TextDecoder('utf-8');
            return decoder.decode(record.payload);
          } catch (e) {
            return '二进制数据';
          }
        }
        return '未知格式';
      }).join('\n');
    }

    return JSON.stringify(data, null, 2);
  }
}

// 创建全局实例
const nfcManager = new NFCManager();

export default nfcManager;

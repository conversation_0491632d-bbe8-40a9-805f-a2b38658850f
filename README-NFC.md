# NFC功能使用说明

## 概述

本项目为uniapp微信小程序添加了完整的NFC（近场通信）功能，包括NFC数据的读取、写入和记录管理。

## 功能特性

### 🔍 NFC读取功能
- 自动检测NFC标签
- 读取NDEF格式数据
- 显示标签详细信息
- 支持多种数据格式解析

### ✏️ NFC写入功能
- 支持多种数据类型写入：
  - 纯文本
  - 网址链接
  - WiFi配置信息
  - 联系人信息
- 提供快捷模板
- 实时数据预览

### 📋 记录管理功能
- 自动记录所有NFC操作
- 按类型筛选记录（读取/写入/错误）
- 操作统计和成功率分析
- 记录导出和分享功能

## 文件结构

```
├── utils/
│   └── nfc.js                 # NFC工具类，核心功能实现
├── pages/
│   ├── index/
│   │   └── index.uvue         # 主页面，NFC功能入口
│   ├── nfc-read/
│   │   └── nfc-read.uvue      # NFC读取页面
│   ├── nfc-write/
│   │   └── nfc-write.uvue     # NFC写入页面
│   └── nfc-records/
│       └── nfc-records.uvue   # 记录管理页面
├── test/
│   └── nfc-test.js            # NFC功能测试脚本
└── README-NFC.md              # 本说明文档
```

## 使用方法

### 1. 权限配置

项目已在 `manifest.json` 中配置了必要的NFC权限：

```json
{
  "mp-weixin": {
    "requiredPrivateInfos": ["getNFCAdapter"],
    "permission": {
      "scope.writePhotosAlbum": {
        "desc": "用于保存NFC数据记录"
      }
    }
  }
}
```

### 2. 基本使用流程

#### 读取NFC标签
1. 打开小程序，进入主页
2. 点击"读取NFC"按钮
3. 点击"开始扫描"
4. 将NFC标签靠近设备
5. 查看读取到的数据

#### 写入NFC标签
1. 进入"写入NFC"页面
2. 选择数据类型（文本/网址/WiFi/联系人）
3. 输入要写入的数据
4. 点击"扫描标签"
5. 将NFC标签靠近设备
6. 点击"写入数据"

#### 查看操作记录
1. 进入"操作记录"页面
2. 查看所有NFC操作历史
3. 可按类型筛选记录
4. 支持导出和清空记录

### 3. API使用示例

```javascript
import nfcManager from '@/utils/nfc.js'

// 检查NFC可用性
const status = await nfcManager.checkNFCAvailability();
console.log('NFC状态:', status);

// 开始NFC发现
await nfcManager.startDiscovery();

// 监听标签发现
uni.$on('nfc-tag-found', (tag) => {
  console.log('发现标签:', tag);
});

// 读取NDEF数据
const data = await nfcManager.readNDEF(tagId);

// 写入NDEF数据
await nfcManager.writeNDEF('Hello NFC!', tagId);

// 获取操作记录
const records = nfcManager.getRecords();
```

## 技术实现

### 核心类：NFCManager

位于 `utils/nfc.js`，提供以下主要方法：

- `init()` - 初始化NFC适配器
- `checkNFCAvailability()` - 检查NFC可用性
- `startDiscovery()` - 开始NFC发现
- `stopDiscovery()` - 停止NFC发现
- `readNDEF(tagId)` - 读取NDEF数据
- `writeNDEF(data, tagId)` - 写入NDEF数据
- `getRecords()` - 获取操作记录
- `formatNFCData(data)` - 格式化NFC数据

### 事件系统

使用uni-app的事件系统进行页面间通信：

- `nfc-tag-found` - 标签发现事件

### 数据存储

- 操作记录存储在内存中
- 支持导出为JSON格式
- 可扩展为持久化存储

## 测试

运行测试脚本验证功能：

```bash
node test/nfc-test.js
```

测试覆盖：
- NFC可用性检查
- 标签发现和读取
- 数据写入
- 记录管理
- 数据格式化

## 注意事项

### 设备要求
- 设备必须支持NFC功能
- 需要在设备设置中开启NFC
- 微信小程序需要最新版本

### 使用限制
- 仅支持NDEF格式的NFC标签
- 写入数据大小受标签容量限制
- 需要用户授权NFC权限

### 兼容性
- 支持微信小程序
- 需要uni-app x框架
- 建议在真机上测试

## 常见问题

### Q: NFC功能无法使用？
A: 请检查：
1. 设备是否支持NFC
2. NFC是否已开启
3. 微信版本是否最新
4. 小程序权限是否正确配置

### Q: 无法读取某些NFC标签？
A: 可能原因：
1. 标签格式不是NDEF
2. 标签已损坏
3. 标签被加密或保护

### Q: 写入失败？
A: 检查：
1. 标签是否可写
2. 标签容量是否足够
3. 数据格式是否正确

## 扩展功能

可以考虑添加的功能：
- 支持更多NFC标签类型
- 数据加密和解密
- 批量操作
- 云端同步
- 二维码转换

## 技术支持

如有问题，请检查：
1. 控制台错误信息
2. NFC设备状态
3. 网络连接状态
4. 小程序版本和权限

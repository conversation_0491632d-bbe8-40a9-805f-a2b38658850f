<view id="{{Z}}" change:eS="{{uV.sS}}" eS="{{$eS[Z]}}" change:eA="{{uV.sA}}" eA="{{$eA[Z]}}" class="{{['container', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}"><view class="header"><text class="page-title">NFC写入</text><text class="page-subtitle">输入数据并写入到NFC标签</text></view><view class="input-section"><view class="section-title">输入数据</view><view class="data-type-selector"><view class="{{['type-item', a && 'active']}}" bindtap="{{b}}"><text class="type-icon">📝</text><text class="type-label">文本</text></view><view class="{{['type-item', c && 'active']}}" bindtap="{{d}}"><text class="type-icon">🔗</text><text class="type-label">网址</text></view><view class="{{['type-item', e && 'active']}}" bindtap="{{f}}"><text class="type-icon">📶</text><text class="type-label">WiFi</text></view><view class="{{['type-item', g && 'active']}}" bindtap="{{h}}"><text class="type-icon">👤</text><text class="type-label">联系人</text></view></view><view wx:if="{{i}}" class="input-area"><block wx:if="{{r0}}"><textarea class="data-input" placeholder="请输入要写入的文本内容" maxlength="{{500}}" value="{{j}}" bindinput="{{k}}">
				</textarea></block><text class="char-count">{{l}}/500</text></view><view wx:if="{{m}}" class="input-area"><input class="data-input" placeholder="请输入网址，如：https://www.example.com" type="url" value="{{n}}" bindinput="{{o}}"></input></view><view wx:if="{{p}}" class="input-area"><input class="data-input" placeholder="WiFi名称(SSID)" style="margin-bottom:15px" value="{{q}}" bindinput="{{r}}"></input><input class="data-input" placeholder="WiFi密码" type="password" style="margin-bottom:15px" value="{{s}}" bindinput="{{t}}"></input><picker bindchange="{{w}}" value="{{x}}" range="{{y}}"><view class="picker-input"><text>加密类型: {{v}}</text><text class="picker-arrow">&gt;</text></view></picker></view><view wx:if="{{z}}" class="input-area"><input class="data-input" placeholder="姓名" style="margin-bottom:15px" value="{{A}}" bindinput="{{B}}"></input><input class="data-input" placeholder="电话号码" type="tel" style="margin-bottom:15px" value="{{C}}" bindinput="{{D}}"></input><input class="data-input" placeholder="邮箱地址" type="email" value="{{E}}" bindinput="{{F}}"></input></view></view><view wx:if="{{G}}" class="preview-section"><view class="section-title">数据预览</view><view class="preview-content"><text class="preview-text">{{H}}</text></view></view><view class="scan-status"><view class="{{['scan-indicator', J && 'scanning', K && 'found']}}"><text class="scan-icon">{{I}}</text></view><text class="scan-text">{{L}}</text></view><view class="actions"><button class="action-btn primary" bindtap="{{N}}" disabled="{{O}}">{{M}}</button><button class="action-btn secondary" bindtap="{{Q}}" disabled="{{R}}">{{P}}</button></view><view class="templates-section"><view class="section-title">快捷模板</view><view class="template-list"><view class="template-item" bindtap="{{S}}"><text class="template-icon">👋</text><text class="template-label">问候语</text></view><view class="template-item" bindtap="{{T}}"><text class="template-icon">🌐</text><text class="template-label">官网</text></view><view class="template-item" bindtap="{{U}}"><text class="template-icon">📞</text><text class="template-label">联系方式</text></view></view></view><view wx:if="{{V}}" class="error-message"><text class="error-text">{{W}}</text></view><view wx:if="{{X}}" class="success-message"><text class="success-text">{{Y}}</text></view></view><wxs src="/common/uniView.wxs" module="uV"/>

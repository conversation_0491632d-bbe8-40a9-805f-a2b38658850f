"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_nfc = require("../../utils/nfc.js");
const _sfc_main = common_vendor.defineComponent({
  data() {
    return {
      dataType: "text",
      inputData: new UTSJSONObject({
        text: "",
        url: "",
        wifi: new UTSJSONObject({
          ssid: "",
          password: "",
          security: "WPA"
        }),
        contact: new UTSJSONObject({
          name: "",
          phone: "",
          email: ""
        })
      }),
      securityTypes: ["WPA", "WEP", "Open"],
      securityIndex: 0,
      isScanning: false,
      tagFound: false,
      currentTag: null,
      nfcAvailable: false,
      isWriting: false,
      errorMessage: "",
      successMessage: ""
    };
  },
  computed: {
    previewData() {
      switch (this.dataType) {
        case "text":
          return this.inputData.text;
        case "url":
          return this.inputData.url;
        case "wifi":
          return `WiFi: ${this.inputData.wifi.ssid}
密码: ${this.inputData.wifi.password}
加密: ${this.securityTypes[this.securityIndex]}`;
        case "contact":
          return `姓名: ${this.inputData.contact.name}
电话: ${this.inputData.contact.phone}
邮箱: ${this.inputData.contact.email}`;
        default:
          return "";
      }
    },
    canWrite() {
      return this.tagFound && this.previewData.trim() && this.nfcAvailable;
    }
  },
  onLoad() {
    return common_vendor.__awaiter(this, void 0, void 0, function* () {
      yield this.checkNFCStatus();
      common_vendor.index.$on("nfc-tag-found", this.onTagFound);
    });
  },
  onUnload() {
    common_vendor.index.$off("nfc-tag-found", this.onTagFound);
    if (this.isScanning) {
      utils_nfc.nfcManager.stopDiscovery();
    }
  },
  methods: {
    checkNFCStatus() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        try {
          const status = yield utils_nfc.nfcManager.checkNFCAvailability();
          this.nfcAvailable = status.available;
          if (!status.available) {
            this.errorMessage = status.message;
          }
        } catch (error) {
          this.nfcAvailable = false;
          this.errorMessage = "检查NFC状态失败: " + error.message;
        }
      });
    },
    selectDataType(type = null) {
      this.dataType = type;
      this.clearMessages();
    },
    onSecurityChange(e = null) {
      this.securityIndex = e.detail.value;
      this.inputData.wifi.security = this.securityTypes[this.securityIndex];
    },
    toggleScan() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        try {
          if (this.isScanning) {
            yield utils_nfc.nfcManager.stopDiscovery();
            this.isScanning = false;
            common_vendor.index.showToast({ title: "已停止扫描", icon: "success" });
          } else {
            yield utils_nfc.nfcManager.startDiscovery();
            this.isScanning = true;
            this.clearMessages();
            common_vendor.index.showToast({ title: "开始扫描NFC", icon: "success" });
          }
        } catch (error) {
          this.errorMessage = "操作失败: " + error.message;
          common_vendor.index.__f__("error", "at pages/nfc-write/nfc-write.uvue:259", "切换扫描状态失败:", error);
        }
      });
    },
    onTagFound(tag = null) {
      common_vendor.index.__f__("log", "at pages/nfc-write/nfc-write.uvue:264", "写入页面收到NFC标签:", tag);
      this.currentTag = tag;
      this.tagFound = true;
      this.clearMessages();
      common_vendor.index.showToast({ title: "发现NFC标签", icon: "success" });
    },
    writeData() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        if (!this.canWrite)
          return Promise.resolve(null);
        this.isWriting = true;
        this.clearMessages();
        try {
          const dataToWrite = this.formatDataForWriting();
          yield utils_nfc.nfcManager.writeNDEF(dataToWrite, this.currentTag.tagId);
          this.successMessage = "数据写入成功！";
          common_vendor.index.showToast({ title: "写入成功", icon: "success" });
          setTimeout(() => {
            this.successMessage = "";
          }, 3e3);
        } catch (error) {
          this.errorMessage = "写入失败: " + error.message;
          common_vendor.index.__f__("error", "at pages/nfc-write/nfc-write.uvue:292", "写入数据失败:", error);
        } finally {
          this.isWriting = false;
        }
      });
    },
    formatDataForWriting() {
      switch (this.dataType) {
        case "text":
          return this.inputData.text;
        case "url":
          return this.inputData.url;
        case "wifi":
          return `WIFI:T:${this.inputData.wifi.security};S:${this.inputData.wifi.ssid};P:${this.inputData.wifi.password};;`;
        case "contact":
          return `BEGIN:VCARD
VERSION:3.0
FN:${this.inputData.contact.name}
TEL:${this.inputData.contact.phone}
EMAIL:${this.inputData.contact.email}
END:VCARD`;
        default:
          return "";
      }
    },
    useTemplate(templateType = null) {
      switch (templateType) {
        case "hello":
          this.dataType = "text";
          this.inputData.text = "你好！欢迎使用NFC功能！";
          break;
        case "website":
          this.dataType = "url";
          this.inputData.url = "https://www.example.com";
          break;
        case "contact":
          this.dataType = "contact";
          this.inputData.contact = {
            name: "张三",
            phone: "13800138000",
            email: "<EMAIL>"
          };
          break;
      }
      this.clearMessages();
    },
    clearMessages() {
      this.errorMessage = "";
      this.successMessage = "";
    }
  }
});
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.dataType === "text" ? 1 : "",
    b: common_vendor.o(($event) => $options.selectDataType("text")),
    c: $data.dataType === "url" ? 1 : "",
    d: common_vendor.o(($event) => $options.selectDataType("url")),
    e: $data.dataType === "wifi" ? 1 : "",
    f: common_vendor.o(($event) => $options.selectDataType("wifi")),
    g: $data.dataType === "contact" ? 1 : "",
    h: common_vendor.o(($event) => $options.selectDataType("contact")),
    i: $data.dataType === "text"
  }, $data.dataType === "text" ? {
    j: $data.inputData.text,
    k: common_vendor.o(($event) => $data.inputData.text = $event.detail.value),
    l: common_vendor.t($data.inputData.text.length)
  } : {}, {
    m: $data.dataType === "url"
  }, $data.dataType === "url" ? {
    n: $data.inputData.url,
    o: common_vendor.o(($event) => $data.inputData.url = $event.detail.value)
  } : {}, {
    p: $data.dataType === "wifi"
  }, $data.dataType === "wifi" ? {
    q: $data.inputData.wifi.ssid,
    r: common_vendor.o(($event) => $data.inputData.wifi.ssid = $event.detail.value),
    s: $data.inputData.wifi.password,
    t: common_vendor.o(($event) => $data.inputData.wifi.password = $event.detail.value),
    v: common_vendor.t($data.securityTypes[$data.securityIndex]),
    w: common_vendor.o((...args) => $options.onSecurityChange && $options.onSecurityChange(...args)),
    x: $data.securityIndex,
    y: $data.securityTypes
  } : {}, {
    z: $data.dataType === "contact"
  }, $data.dataType === "contact" ? {
    A: $data.inputData.contact.name,
    B: common_vendor.o(($event) => $data.inputData.contact.name = $event.detail.value),
    C: $data.inputData.contact.phone,
    D: common_vendor.o(($event) => $data.inputData.contact.phone = $event.detail.value),
    E: $data.inputData.contact.email,
    F: common_vendor.o(($event) => $data.inputData.contact.email = $event.detail.value)
  } : {}, {
    G: $options.previewData
  }, $options.previewData ? {
    H: common_vendor.t($options.previewData)
  } : {}, {
    I: common_vendor.t($data.tagFound ? "✅" : $data.isScanning ? "📡" : "📱"),
    J: $data.isScanning ? 1 : "",
    K: $data.tagFound ? 1 : "",
    L: common_vendor.t($data.tagFound ? "已发现标签，可以写入" : $data.isScanning ? "请将标签靠近设备" : "点击开始扫描标签"),
    M: common_vendor.t($data.isScanning ? "停止扫描" : "扫描标签"),
    N: common_vendor.o((...args) => $options.toggleScan && $options.toggleScan(...args)),
    O: !$data.nfcAvailable,
    P: common_vendor.t($data.isWriting ? "写入中..." : "写入数据"),
    Q: common_vendor.o((...args) => $options.writeData && $options.writeData(...args)),
    R: !$options.canWrite || $data.isWriting,
    S: common_vendor.o(($event) => $options.useTemplate("hello")),
    T: common_vendor.o(($event) => $options.useTemplate("website")),
    U: common_vendor.o(($event) => $options.useTemplate("contact")),
    V: $data.errorMessage
  }, $data.errorMessage ? {
    W: common_vendor.t($data.errorMessage)
  } : {}, {
    X: $data.successMessage
  }, $data.successMessage ? {
    Y: common_vendor.t($data.successMessage)
  } : {}, {
    Z: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/nfc-write/nfc-write.js.map

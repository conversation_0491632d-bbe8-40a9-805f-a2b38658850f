@import "../../uvue.wxss";
:host{display:flex;flex-direction:column}

.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
}
.header {
		text-align: center;
		margin-bottom: 30px;
}
.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8px;
}
.page-subtitle {
		font-size: 14px;
		color: #666;
}

	/* 输入区域 */
.input-section, .preview-section, .templates-section {
		background: white;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
		display: block;
}

	/* 数据类型选择器 */
.data-type-selector {
		display: flex;
		gap: 10px;
		margin-bottom: 20px;
		flex-wrap: wrap;
}
.type-item {
		flex: 1;
		min-width: 70px;
		height: 60px;
		border: 2px solid #f0f0f0;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: white;
		transition: all 0.3s ease;
}
.type-item.active {
		border-color: #1890ff;
		background: #e6f7ff;
}
.type-icon {
		font-size: 20px;
		margin-bottom: 4px;
}
.type-label {
		font-size: 12px;
		color: #666;
}
.type-item.active .type-label {
		color: #1890ff;
		font-weight: bold;
}

	/* 输入区域 */
.input-area {
		margin-bottom: 15px;
}
.data-input {
		width: 100%;
		padding: 12px 15px;
		border: 1px solid #d9d9d9;
		border-radius: 8px;
		font-size: 16px;
		background: white;
		box-sizing: border-box;
}
.data-input:focus {
		border-color: #1890ff;
		outline: none;
}
textarea.data-input {
		min-height: 100px;
		resize: vertical;
}
.char-count {
		font-size: 12px;
		color: #999;
		text-align: right;
		margin-top: 5px;
		display: block;
}
.picker-input {
		padding: 12px 15px;
		border: 1px solid #d9d9d9;
		border-radius: 8px;
		background: white;
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.picker-arrow {
		color: #999;
		font-size: 16px;
}

	/* 预览区域 */
.preview-content {
		background: #f8f9fa;
		border-radius: 8px;
		padding: 15px;
}
.preview-text {
		font-size: 14px;
		color: #333;
		line-height: 1.6;
		word-break: break-all;
}

	/* 扫描状态 */
.scan-status {
		text-align: center;
		margin-bottom: 30px;
}
.scan-indicator {
		width: 100px;
		height: 100px;
		border-radius: 50px;
		background: #f0f0f0;
		margin: 0 auto 15px;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
}
.scan-indicator.scanning {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		animation: pulse 2s infinite;
}
.scan-indicator.found {
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}
@keyframes pulse {
0% { transform: scale(1);
}
50% { transform: scale(1.1);
}
100% { transform: scale(1);
}
}
.scan-icon {
		font-size: 35px;
		color: white;
}
.scan-text {
		font-size: 16px;
		color: #666;
		font-weight: bold;
}

	/* 操作按钮 */
.actions {
		margin-bottom: 30px;
}
.action-btn {
		width: 100%;
		height: 50px;
		border-radius: 12px;
		margin-bottom: 15px;
		border: none;
		font-size: 16px;
		font-weight: bold;
}
.action-btn:last-child {
		margin-bottom: 0;
}
.primary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
}
.secondary {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		color: white;
}
.action-btn[disabled] {
		background: #d9d9d9 !important;
		color: #999 !important;
}

	/* 模板列表 */
.template-list {
		display: flex;
		gap: 15px;
}
.template-item {
		flex: 1;
		height: 70px;
		border: 1px solid #f0f0f0;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #fafafa;
		transition: all 0.3s ease;
}
.template-item:active {
		background: #e6f7ff;
		border-color: #1890ff;
}
.template-icon {
		font-size: 24px;
		margin-bottom: 5px;
}
.template-label {
		font-size: 12px;
		color: #666;
}

	/* 消息提示 */
.error-message {
		background: #fff2f0;
		border: 1px solid #ffccc7;
		border-radius: 8px;
		padding: 15px;
		margin-top: 20px;
}
.error-text {
		font-size: 14px;
		color: #ff4d4f;
		line-height: 1.4;
}
.success-message {
		background: #f6ffed;
		border: 1px solid #b7eb8f;
		border-radius: 8px;
		padding: 15px;
		margin-top: 20px;
}
.success-text {
		font-size: 14px;
		color: #52c41a;
		line-height: 1.4;
}

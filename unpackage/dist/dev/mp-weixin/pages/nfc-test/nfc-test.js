"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_nfc = require("../../utils/nfc.js");
const _sfc_main = common_vendor.defineComponent({
  data() {
    return {
      testResults: [],
      logs: [],
      isRunning: false
    };
  },
  onLoad() {
    this.addLog("页面加载完成");
  },
  methods: {
    runTests() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        this.isRunning = true;
        this.testResults = [];
        this.logs = [];
        this.addLog("开始运行NFC功能测试");
        yield this.testNFCInit();
        yield this.testNFCAvailability();
        yield this.testDiscovery();
        yield this.testRecordManagement();
        this.isRunning = false;
        this.addLog("所有测试完成");
      });
    },
    testNFCInit() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        const testName = "NFC适配器初始化";
        this.addLog(`开始测试: ${testName}`);
        try {
          if (utils_nfc.nfcManager && utils_nfc.nfcManager.nfcAdapter) {
            this.addTestResult(testName, "success", "NFC适配器初始化成功");
            this.addLog("✅ NFC适配器已正确初始化");
          } else {
            this.addTestResult(testName, "error", "NFC适配器未初始化");
            this.addLog("❌ NFC适配器初始化失败");
          }
        } catch (error) {
          this.addTestResult(testName, "error", `初始化失败: ${error.message}`);
          this.addLog(`❌ 初始化异常: ${error.message}`);
        }
      });
    },
    testNFCAvailability() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        const testName = "NFC可用性检查";
        this.addLog(`开始测试: ${testName}`);
        try {
          const status = yield utils_nfc.nfcManager.checkNFCAvailability();
          this.addTestResult(testName, status.available ? "success" : "error", status.message);
          this.addLog(`📱 NFC状态: ${status.message}`);
        } catch (error) {
          this.addTestResult(testName, "error", `检查失败: ${error.message}`);
          this.addLog(`❌ 检查异常: ${error.message}`);
        }
      });
    },
    testDiscovery() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        const testName = "NFC发现功能";
        this.addLog(`开始测试: ${testName}`);
        try {
          yield utils_nfc.nfcManager.startDiscovery();
          this.addLog("✅ 开始NFC发现成功");
          yield new Promise((resolve) => {
            return setTimeout(resolve, 2e3);
          });
          yield utils_nfc.nfcManager.stopDiscovery();
          this.addLog("✅ 停止NFC发现成功");
          this.addTestResult(testName, "success", "NFC发现功能正常");
        } catch (error) {
          this.addTestResult(testName, "error", `发现功能失败: ${error.message}`);
          this.addLog(`❌ 发现功能异常: ${error.message}`);
        }
      });
    },
    testRecordManagement() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        const testName = "记录管理功能";
        this.addLog(`开始测试: ${testName}`);
        try {
          utils_nfc.nfcManager.clearRecords();
          this.addLog("🗑️ 清除现有记录");
          utils_nfc.nfcManager.records.push(new UTSJSONObject({
            id: "test-" + Date.now(),
            type: "read",
            timestamp: /* @__PURE__ */ new Date(),
            tagId: "test-tag",
            data: "测试数据",
            success: true
          }));
          const records = utils_nfc.nfcManager.getRecords();
          if (records.length > 0) {
            this.addLog(`📋 获取到 ${records.length} 条记录`);
            const exportData = utils_nfc.nfcManager.exportRecords();
            if (exportData && exportData.length > 0) {
              this.addLog("📤 记录导出成功");
              this.addTestResult(testName, "success", "记录管理功能正常");
            } else {
              this.addTestResult(testName, "error", "记录导出失败");
            }
          } else {
            this.addTestResult(testName, "error", "无法获取记录");
          }
        } catch (error) {
          this.addTestResult(testName, "error", `记录管理失败: ${error.message}`);
          this.addLog(`❌ 记录管理异常: ${error.message}`);
        }
      });
    },
    addTestResult(name = null, status = null, message = null) {
      this.testResults.push({
        name,
        status,
        message
      });
    },
    addLog(message = null) {
      this.logs.push({
        time: /* @__PURE__ */ new Date(),
        message
      });
      common_vendor.index.__f__("log", "at pages/nfc-test/nfc-test.uvue:189", `[NFC测试] ${message}`);
    },
    clearResults() {
      this.testResults = [];
      this.logs = [];
      this.addLog("清除测试结果");
    },
    formatTime(time = null) {
      return time.toLocaleTimeString();
    }
  }
});
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.testResults, (test, index, i0) => {
      return {
        a: common_vendor.t(test.name),
        b: common_vendor.t(test.status === "success" ? "✅" : test.status === "error" ? "❌" : "⏳"),
        c: common_vendor.n(test.status),
        d: common_vendor.t(test.message),
        e: index
      };
    }),
    b: common_vendor.t($data.isRunning ? "测试中..." : "开始测试"),
    c: common_vendor.o((...args) => $options.runTests && $options.runTests(...args)),
    d: $data.isRunning,
    e: common_vendor.o((...args) => $options.clearResults && $options.clearResults(...args)),
    f: $data.logs.length > 0
  }, $data.logs.length > 0 ? {
    g: common_vendor.f($data.logs, (log, index, i0) => {
      return {
        a: common_vendor.t($options.formatTime(log.time)),
        b: common_vendor.t(log.message),
        c: index
      };
    })
  } : {}, {
    h: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/nfc-test/nfc-test.js.map

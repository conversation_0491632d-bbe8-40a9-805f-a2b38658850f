:host{display:flex;flex-direction:column}

.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
}
.header {
		text-align: center;
		margin-bottom: 30px;
}
.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8px;
}
.page-subtitle {
		font-size: 14px;
		color: #666;
}
.test-results {
		margin-bottom: 30px;
}
.test-item {
		background: white;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 10px;
		box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.test-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 5px;
}
.test-name {
		font-size: 16px;
		font-weight: bold;
		color: #333;
}
.test-status {
		font-size: 18px;
}
.test-message {
		font-size: 14px;
		color: #666;
		line-height: 1.4;
}
.actions {
		margin-bottom: 30px;
}
.action-btn {
		width: 100%;
		height: 50px;
		border-radius: 8px;
		margin-bottom: 15px;
		border: none;
		font-size: 16px;
		font-weight: bold;
}
.primary {
		background: #1890ff;
		color: white;
}
.secondary {
		background: #f5f5f5;
		color: #333;
}
.action-btn[disabled] {
		background: #d9d9d9 !important;
		color: #999 !important;
}
.logs {
		background: white;
		border-radius: 8px;
		padding: 15px;
		box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.section-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
		display: block;
}
.log-item {
		display: flex;
		margin-bottom: 8px;
		font-size: 12px;
}
.log-time {
		color: #999;
		margin-right: 10px;
		min-width: 80px;
}
.log-message {
		color: #333;
		flex: 1;
}

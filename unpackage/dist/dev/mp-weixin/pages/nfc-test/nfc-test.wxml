<view id="{{h}}" change:eS="{{uV.sS}}" eS="{{$eS[h]}}" change:eA="{{uV.sA}}" eA="{{$eA[h]}}" class="{{['container', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}"><view class="header"><text class="page-title">NFC功能测试</text><text class="page-subtitle">测试NFC API是否正常工作</text></view><view class="test-results"><view wx:for="{{a}}" wx:for-item="test" wx:key="e" class="test-item"><view class="test-header"><text class="test-name">{{test.a}}</text><text class="{{['test-status', test.c]}}">{{test.b}}</text></view><text class="test-message">{{test.d}}</text></view></view><view class="actions"><button class="action-btn primary" bindtap="{{c}}" disabled="{{d}}">{{b}}</button><button class="action-btn secondary" bindtap="{{e}}"> 清除结果 </button></view><view wx:if="{{f}}" class="logs"><view class="section-title">详细日志</view><view wx:for="{{g}}" wx:for-item="log" wx:key="c" class="log-item"><text class="log-time">{{log.a}}</text><text class="log-message">{{log.b}}</text></view></view></view><wxs src="/common/uniView.wxs" module="uV"/>

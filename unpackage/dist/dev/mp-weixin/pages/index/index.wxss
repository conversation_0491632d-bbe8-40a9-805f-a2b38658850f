@import "../../uvue.wxss";
:host{display:flex;flex-direction:column}

.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
}
.logo {
		height: 80px;
		width: 80px;
		margin: 20px auto 15px auto;
		display: block;
}
.title {
		font-size: 24px;
		color: #333;
		text-align: center;
		font-weight: bold;
		margin-bottom: 30px;
}

	/* NFC状态显示 */
.nfc-status {
		background: white;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.status-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
}
.status-item:last-child {
		margin-bottom: 0;
}
.status-label {
		font-size: 16px;
		color: #666;
}
.status-value {
		font-size: 16px;
		font-weight: bold;
}
.status-success {
		color: #52c41a;
}
.status-error {
		color: #ff4d4f;
}
.status-normal {
		color: #1890ff;
}

	/* 功能按钮 */
.nfc-actions {
		margin-bottom: 20px;
}
.action-btn {
		width: 100%;
		height: 60px;
		border-radius: 12px;
		margin-bottom: 15px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: none;
		font-size: 16px;
		font-weight: bold;
}
.action-btn:last-child {
		margin-bottom: 0;
}
.primary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
}
.secondary {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		color: white;
}
.tertiary {
		background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
		color: white;
}
.action-btn[disabled] {
		background: #d9d9d9 !important;
		color: #999 !important;
}
.btn-icon {
		font-size: 20px;
		margin-right: 10px;
}
.btn-text {
		font-size: 16px;
}

	/* 快速操作 */
.quick-actions {
		display: flex;
		gap: 10px;
		margin-bottom: 20px;
}
.quick-btn {
		flex: 1;
		height: 40px;
		border-radius: 8px;
		border: none;
		font-size: 14px;
		font-weight: bold;
}
.start-btn {
		background: #52c41a;
		color: white;
}
.stop-btn {
		background: #ff4d4f;
		color: white;
}
.refresh-btn {
		background: #1890ff;
		color: white;
}

	/* 最近记录 */
.recent-records {
		background: white;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
}
.record-item {
		padding: 15px;
		border: 1px solid #f0f0f0;
		border-radius: 8px;
		margin-bottom: 10px;
		background: #fafafa;
}
.record-item:last-child {
		margin-bottom: 0;
}
.record-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8px;
}
.record-type {
		font-size: 12px;
		padding: 2px 8px;
		border-radius: 4px;
		color: white;
		font-weight: bold;
}
.record-type.read {
		background: #52c41a;
}
.record-type.write {
		background: #1890ff;
}
.record-time {
		font-size: 12px;
		color: #999;
}
.record-content {
		font-size: 14px;
		color: #666;
		line-height: 1.4;
}

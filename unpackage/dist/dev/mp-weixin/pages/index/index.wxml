<view id="{{t}}" change:eS="{{uV.sS}}" eS="{{$eS[t]}}" change:eA="{{uV.sA}}" eA="{{$eA[t]}}" class="{{['container', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}"><image class="logo" src="{{a}}"></image><view class="text-area"><text class="title">{{b}}</text></view><view class="nfc-status"><view class="status-item"><text class="status-label">NFC状态:</text><text class="{{['status-value', d]}}">{{c}}</text></view><view wx:if="{{e}}" class="status-item"><text class="status-label">发现状态:</text><text class="{{['status-value', g]}}">{{f}}</text></view></view><view class="nfc-actions"><button class="action-btn primary" bindtap="{{h}}" disabled="{{i}}"><text class="btn-icon">📖</text><text class="btn-text">读取NFC</text></button><button class="action-btn secondary" bindtap="{{j}}" disabled="{{k}}"><text class="btn-icon">✏️</text><text class="btn-text">写入NFC</text></button><button class="action-btn tertiary" bindtap="{{l}}"><text class="btn-icon">📋</text><text class="btn-text">操作记录</text></button></view><view wx:if="{{m}}" class="quick-actions"><button bindtap="{{o}}" class="{{['quick-btn', p]}}">{{n}}</button><button class="quick-btn refresh-btn" bindtap="{{q}}"> 刷新状态 </button></view><view wx:if="{{r}}" class="recent-records"><view class="section-title">最近操作</view><view wx:for="{{s}}" wx:for-item="record" wx:key="e" class="record-item" bindtap="{{record.f}}"><view class="record-header"><text class="{{['record-type', record.b]}}">{{record.a}}</text><text class="record-time">{{record.c}}</text></view><text class="record-content">{{record.d}}</text></view></view></view><wxs src="/common/uniView.wxs" module="uV"/>

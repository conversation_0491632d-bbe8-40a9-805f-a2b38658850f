"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_nfc = require("../../utils/nfc.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = common_vendor.defineComponent({
  data() {
    return {
      title: "NFC管理工具",
      nfcStatus: new UTSJSONObject({
        available: false,
        message: "检查中..."
      }),
      isDiscovering: false,
      recentRecords: []
    };
  },
  onLoad() {
    return common_vendor.__awaiter(this, void 0, void 0, function* () {
      yield this.initNFC();
      this.loadRecentRecords();
      common_vendor.index.$on("nfc-tag-found", this.onTagFound);
    });
  },
  onUnload() {
    common_vendor.index.$off("nfc-tag-found", this.onTagFound);
    if (this.isDiscovering) {
      utils_nfc.nfcManager.stopDiscovery();
    }
  },
  methods: {
    initNFC() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        try {
          const status = yield utils_nfc.nfcManager.checkNFCAvailability();
          this.nfcStatus = status;
          this.isDiscovering = status.discovering || false;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/index/index.uvue:105", "初始化NFC失败:", error);
          this.nfcStatus = {
            available: false,
            message: "初始化失败: " + error.message
          };
        }
      });
    },
    refreshNFCStatus() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        common_vendor.index.showLoading({ title: "检查中..." });
        yield this.initNFC();
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: this.nfcStatus.available ? "NFC可用" : "NFC不可用",
          icon: this.nfcStatus.available ? "success" : "error"
        });
      });
    },
    toggleDiscovery() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        try {
          if (this.isDiscovering) {
            yield utils_nfc.nfcManager.stopDiscovery();
            this.isDiscovering = false;
            common_vendor.index.showToast({ title: "已停止扫描", icon: "success" });
          } else {
            yield utils_nfc.nfcManager.startDiscovery();
            this.isDiscovering = true;
            common_vendor.index.showToast({ title: "开始扫描NFC", icon: "success" });
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/index/index.uvue:135", "切换发现状态失败:", error);
          common_vendor.index.showToast({
            title: "操作失败: " + error.message,
            icon: "error"
          });
        }
      });
    },
    goToRead() {
      common_vendor.index.navigateTo({
        url: "/pages/nfc-read/nfc-read"
      });
    },
    goToWrite() {
      common_vendor.index.navigateTo({
        url: "/pages/nfc-write/nfc-write"
      });
    },
    goToRecords() {
      common_vendor.index.navigateTo({
        url: "/pages/nfc-records/nfc-records"
      });
    },
    loadRecentRecords() {
      this.recentRecords = utils_nfc.nfcManager.getRecords(3);
    },
    onTagFound(record = null) {
      common_vendor.index.__f__("log", "at pages/index/index.uvue:166", "主页面收到NFC标签:", record);
      this.loadRecentRecords();
      common_vendor.index.showModal(new UTSJSONObject({
        title: "发现NFC标签",
        content: `标签ID: ${record.tagId}
是否查看详情？`,
        success: (res) => {
          if (res.confirm) {
            this.viewRecord(record);
          }
        }
      }));
    },
    viewRecord(record = null) {
      common_vendor.index.navigateTo({
        url: `/pages/nfc-read/nfc-read?recordId=${record.id}`
      });
    },
    formatTime(timestamp = null) {
      const date = new Date(timestamp);
      return `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    },
    getRecordPreview(record = null) {
      if (record.error) {
        return `错误: ${record.error}`;
      }
      if (record.type === "write") {
        return `写入: ${record.data ? record.data.substring(0, 20) + "..." : "无数据"}`;
      }
      if (record.messages && record.messages.length > 0) {
        const preview = utils_nfc.nfcManager.formatNFCData(record.messages[0]);
        return preview.length > 30 ? preview.substring(0, 30) + "..." : preview;
      }
      return "无数据";
    }
  }
});
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0,
    b: common_vendor.t($data.title),
    c: common_vendor.t($data.nfcStatus.message),
    d: common_vendor.n($data.nfcStatus.available ? "status-success" : "status-error"),
    e: $data.nfcStatus.available
  }, $data.nfcStatus.available ? {
    f: common_vendor.t($data.isDiscovering ? "正在扫描" : "未扫描"),
    g: common_vendor.n($data.isDiscovering ? "status-success" : "status-normal")
  } : {}, {
    h: common_vendor.o((...args) => $options.goToRead && $options.goToRead(...args)),
    i: !$data.nfcStatus.available,
    j: common_vendor.o((...args) => $options.goToWrite && $options.goToWrite(...args)),
    k: !$data.nfcStatus.available,
    l: common_vendor.o((...args) => $options.goToRecords && $options.goToRecords(...args)),
    m: $data.nfcStatus.available
  }, $data.nfcStatus.available ? {
    n: common_vendor.t($data.isDiscovering ? "停止扫描" : "开始扫描"),
    o: common_vendor.o((...args) => $options.toggleDiscovery && $options.toggleDiscovery(...args)),
    p: common_vendor.n($data.isDiscovering ? "stop-btn" : "start-btn"),
    q: common_vendor.o((...args) => $options.refreshNFCStatus && $options.refreshNFCStatus(...args))
  } : {}, {
    r: $data.recentRecords.length > 0
  }, $data.recentRecords.length > 0 ? {
    s: common_vendor.f($data.recentRecords, (record, k0, i0) => {
      return {
        a: common_vendor.t(record.type === "read" ? "读取" : "写入"),
        b: common_vendor.n(record.type),
        c: common_vendor.t($options.formatTime(record.timestamp)),
        d: common_vendor.t($options.getRecordPreview(record)),
        e: record.id,
        f: common_vendor.o(($event) => $options.viewRecord(record), record.id)
      };
    })
  } : {}, {
    t: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map

<view id="{{x}}" change:eS="{{uV.sS}}" eS="{{$eS[x]}}" change:eA="{{uV.sA}}" eA="{{$eA[x]}}" class="{{['container', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}"><view class="header"><text class="page-title">操作记录</text><text class="page-subtitle">管理NFC读取和写入历史记录</text></view><view class="stats-section"><view class="stat-item"><text class="stat-number">{{a}}</text><text class="stat-label">总记录</text></view><view class="stat-item"><text class="stat-number">{{b}}</text><text class="stat-label">读取</text></view><view class="stat-item"><text class="stat-number">{{c}}</text><text class="stat-label">写入</text></view><view class="stat-item"><text class="stat-number">{{d}}%</text><text class="stat-label">成功率</text></view></view><view class="filter-section"><view class="filter-tabs"><view class="{{['filter-tab', e && 'active']}}" bindtap="{{f}}"> 全部 </view><view class="{{['filter-tab', g && 'active']}}" bindtap="{{h}}"> 读取 </view><view class="{{['filter-tab', i && 'active']}}" bindtap="{{j}}"> 写入 </view><view class="{{['filter-tab', k && 'active']}}" bindtap="{{l}}"> 错误 </view></view><view class="action-buttons"><button class="action-btn export" bindtap="{{m}}"><text class="btn-icon">📤</text><text class="btn-text">导出</text></button><button class="action-btn clear" bindtap="{{n}}"><text class="btn-icon">🗑️</text><text class="btn-text">清空</text></button></view></view><view wx:if="{{o}}" class="records-list"><view wx:for="{{p}}" wx:for-item="record" wx:key="k" class="record-item" bindtap="{{record.l}}" bindlongpress="{{record.m}}"><view class="record-header"><view class="{{['record-type-badge', record.b]}}"><text class="badge-text">{{record.a}}</text></view><view class="{{['record-status', record.d]}}"><text class="status-icon">{{record.c}}</text></view></view><view class="record-content"><text class="record-preview">{{record.e}}</text><text wx:if="{{record.f}}" class="record-tag-id">标签ID: {{record.g}}</text></view><view class="record-footer"><text class="record-time">{{record.h}}</text><view class="record-actions"><text class="action-icon" catchtap="{{record.i}}">📤</text><text class="action-icon" catchtap="{{record.j}}">🗑️</text></view></view></view></view><view wx:else class="empty-state"><text class="empty-icon">📋</text><text class="empty-title">暂无记录</text><text class="empty-subtitle">{{q}}</text><button class="empty-action" bindtap="{{r}}">开始使用NFC</button></view><view wx:if="{{s}}" class="load-more"><button class="load-more-btn" bindtap="{{v}}" disabled="{{w}}">{{t}}</button></view></view><wxs src="/common/uniView.wxs" module="uV"/>

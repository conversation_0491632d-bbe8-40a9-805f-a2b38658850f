@import "../../uvue.wxss";
:host{display:flex;flex-direction:column}

.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
}
.header {
		text-align: center;
		margin-bottom: 30px;
}
.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8px;
}
.page-subtitle {
		font-size: 14px;
		color: #666;
}

	/* 统计信息 */
.stats-section {
		display: flex;
		background: white;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.stat-item {
		flex: 1;
		text-align: center;
}
.stat-number {
		font-size: 24px;
		font-weight: bold;
		color: #1890ff;
		display: block;
		margin-bottom: 5px;
}
.stat-label {
		font-size: 12px;
		color: #666;
}

	/* 筛选区域 */
.filter-section {
		background: white;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.filter-tabs {
		display: flex;
		gap: 10px;
		margin-bottom: 15px;
}
.filter-tab {
		flex: 1;
		height: 35px;
		border: 1px solid #d9d9d9;
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		color: #666;
		background: white;
		transition: all 0.3s ease;
}
.filter-tab.active {
		background: #1890ff;
		color: white;
		border-color: #1890ff;
}
.action-buttons {
		display: flex;
		gap: 10px;
}
.action-btn {
		flex: 1;
		height: 40px;
		border-radius: 8px;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 5px;
		font-size: 14px;
		font-weight: bold;
}
.export {
		background: #52c41a;
		color: white;
}
.clear {
		background: #ff4d4f;
		color: white;
}
.btn-icon {
		font-size: 16px;
}
.btn-text {
		font-size: 14px;
}

	/* 记录列表 */
.records-list {
		margin-bottom: 20px;
}
.record-item {
		background: white;
		border-radius: 12px;
		padding: 15px;
		margin-bottom: 15px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		transition: all 0.3s ease;
}
.record-item:active {
		transform: scale(0.98);
}
.record-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
}
.record-type-badge {
		padding: 4px 8px;
		border-radius: 4px;
		font-size: 12px;
		font-weight: bold;
}
.record-type-badge.read {
		background: #e6f7ff;
		color: #1890ff;
}
.record-type-badge.write {
		background: #f6ffed;
		color: #52c41a;
}
.badge-text {
		font-size: 12px;
}
.record-status {
		display: flex;
		align-items: center;
}
.status-icon {
		font-size: 16px;
}
.record-content {
		margin-bottom: 10px;
}
.record-preview {
		font-size: 14px;
		color: #333;
		line-height: 1.4;
		display: block;
		margin-bottom: 5px;
}
.record-tag-id {
		font-size: 12px;
		color: #999;
}
.record-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.record-time {
		font-size: 12px;
		color: #999;
}
.record-actions {
		display: flex;
		gap: 10px;
}
.action-icon {
		font-size: 16px;
		padding: 5px;
		border-radius: 4px;
		background: #f0f0f0;
		transition: all 0.3s ease;
}
.action-icon:active {
		background: #d9d9d9;
}

	/* 空状态 */
.empty-state {
		text-align: center;
		padding: 60px 20px;
		background: white;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.empty-icon {
		font-size: 60px;
		display: block;
		margin-bottom: 20px;
		opacity: 0.5;
}
.empty-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 10px;
}
.empty-subtitle {
		font-size: 14px;
		color: #666;
		display: block;
		margin-bottom: 30px;
}
.empty-action {
		width: 150px;
		height: 40px;
		background: #1890ff;
		color: white;
		border: none;
		border-radius: 8px;
		font-size: 14px;
		font-weight: bold;
}

	/* 加载更多 */
.load-more {
		text-align: center;
		padding: 20px;
}
.load-more-btn {
		width: 150px;
		height: 40px;
		background: white;
		border: 1px solid #d9d9d9;
		border-radius: 8px;
		font-size: 14px;
		color: #666;
}
.load-more-btn[disabled] {
		background: #f5f5f5;
		color: #999;
}

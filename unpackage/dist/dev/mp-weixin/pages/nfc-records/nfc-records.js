"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_nfc = require("../../utils/nfc.js");
const _sfc_main = common_vendor.defineComponent({
  data() {
    return {
      records: [],
      currentFilter: "all",
      isLoading: false,
      hasMore: false,
      pageSize: 20,
      currentPage: 1
    };
  },
  computed: {
    filteredRecords() {
      let filtered = this.records;
      switch (this.currentFilter) {
        case "read":
          filtered = this.records.filter((r) => {
            return r.type === "read";
          });
          break;
        case "write":
          filtered = this.records.filter((r) => {
            return r.type === "write";
          });
          break;
        case "error":
          filtered = this.records.filter((r) => {
            return !r.success;
          });
          break;
      }
      return filtered;
    },
    totalRecords() {
      return this.records.length;
    },
    readRecords() {
      return this.records.filter((r) => {
        return r.type === "read";
      }).length;
    },
    writeRecords() {
      return this.records.filter((r) => {
        return r.type === "write";
      }).length;
    },
    successRate() {
      if (this.totalRecords === 0)
        return 0;
      const successCount = this.records.filter((r) => {
        return r.success;
      }).length;
      return Math.round(successCount / this.totalRecords * 100);
    }
  },
  onLoad() {
    this.loadRecords();
  },
  onShow() {
    this.loadRecords();
  },
  methods: {
    loadRecords() {
      this.records = utils_nfc.nfcManager.getRecords();
      this.hasMore = false;
    },
    setFilter(filter = null) {
      this.currentFilter = filter;
    },
    viewRecord(record = null) {
      if (record.type === "read") {
        common_vendor.index.navigateTo({
          url: `/pages/nfc-read/nfc-read?recordId=${record.id}`
        });
      } else {
        this.showRecordDetail(record);
      }
    },
    showRecordDetail(record = null) {
      const content = this.getRecordDetail(record);
      common_vendor.index.showModal(new UTSJSONObject({
        title: "记录详情",
        content,
        showCancel: false,
        confirmText: "确定"
      }));
    },
    showRecordActions(record = null) {
      common_vendor.index.showActionSheet({
        itemList: ["查看详情", "分享记录", "删除记录"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.showRecordDetail(record);
              break;
            case 1:
              this.shareRecord(record);
              break;
            case 2:
              this.deleteRecord(record);
              break;
          }
        }
      });
    },
    shareRecord(record = null) {
      const content = this.getRecordDetail(record);
      common_vendor.index.share(new UTSJSONObject({
        provider: "weixin",
        type: 0,
        title: "NFC操作记录",
        summary: content,
        success: () => {
          common_vendor.index.showToast({ title: "分享成功", icon: "success" });
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/nfc-records/nfc-records.uvue:230", "分享失败:", error);
          common_vendor.index.setClipboardData({
            data: content,
            success: () => {
              common_vendor.index.showToast({ title: "已复制到剪贴板", icon: "success" });
            }
          });
        }
      }));
    },
    deleteRecord(record = null) {
      common_vendor.index.showModal(new UTSJSONObject({
        title: "确认删除",
        content: "确定要删除这条记录吗？",
        success: (res) => {
          if (res.confirm) {
            const success = utils_nfc.nfcManager.deleteRecord(record.id);
            if (success) {
              this.loadRecords();
              common_vendor.index.showToast({ title: "删除成功", icon: "success" });
            } else {
              common_vendor.index.showToast({ title: "删除失败", icon: "error" });
            }
          }
        }
      }));
    },
    exportRecords() {
      try {
        const data = utils_nfc.nfcManager.exportRecords();
        common_vendor.index.setClipboardData({
          data,
          success: () => {
            common_vendor.index.showToast({ title: "记录已复制到剪贴板", icon: "success" });
          }
        });
      } catch (error) {
        common_vendor.index.showToast({ title: "导出失败", icon: "error" });
      }
    },
    confirmClearRecords() {
      common_vendor.index.showModal(new UTSJSONObject({
        title: "确认清空",
        content: "确定要清空所有记录吗？此操作不可恢复。",
        success: (res) => {
          if (res.confirm) {
            utils_nfc.nfcManager.clearRecords();
            this.loadRecords();
            common_vendor.index.showToast({ title: "记录已清空", icon: "success" });
          }
        }
      }));
    },
    loadMore() {
      this.isLoading = true;
      setTimeout(() => {
        this.isLoading = false;
      }, 1e3);
    },
    goToRead() {
      common_vendor.index.navigateTo({
        url: "/pages/nfc-read/nfc-read"
      });
    },
    getRecordPreview(record = null) {
      if (record.error) {
        return `错误: ${record.error}`;
      }
      if (record.type === "write") {
        return `写入: ${record.data ? record.data.substring(0, 30) + "..." : "无数据"}`;
      }
      if (record.messages && record.messages.length > 0) {
        const preview = utils_nfc.nfcManager.formatNFCData(record.messages[0]);
        return preview.length > 50 ? preview.substring(0, 50) + "..." : preview;
      }
      return "无数据";
    },
    getRecordDetail(record = null) {
      let detail = `类型: ${record.type === "read" ? "读取" : "写入"}
`;
      detail += `时间: ${this.formatDateTime(record.timestamp)}
`;
      detail += `状态: ${record.success ? "成功" : "失败"}
`;
      if (record.tagId) {
        detail += `标签ID: ${record.tagId}
`;
      }
      if (record.error) {
        detail += `错误: ${record.error}
`;
      } else if (record.data) {
        detail += `数据: ${record.data}
`;
      } else if (record.messages) {
        detail += `数据: ${utils_nfc.nfcManager.formatNFCData(record.messages)}
`;
      }
      return detail;
    },
    getEmptyMessage() {
      switch (this.currentFilter) {
        case "read":
          return "还没有读取记录";
        case "write":
          return "还没有写入记录";
        case "error":
          return "没有错误记录";
        default:
          return "开始使用NFC功能来创建记录";
      }
    },
    formatDateTime(timestamp = null) {
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    }
  }
});
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.totalRecords),
    b: common_vendor.t($options.readRecords),
    c: common_vendor.t($options.writeRecords),
    d: common_vendor.t($options.successRate),
    e: $data.currentFilter === "all" ? 1 : "",
    f: common_vendor.o(($event) => $options.setFilter("all")),
    g: $data.currentFilter === "read" ? 1 : "",
    h: common_vendor.o(($event) => $options.setFilter("read")),
    i: $data.currentFilter === "write" ? 1 : "",
    j: common_vendor.o(($event) => $options.setFilter("write")),
    k: $data.currentFilter === "error" ? 1 : "",
    l: common_vendor.o(($event) => $options.setFilter("error")),
    m: common_vendor.o((...args) => $options.exportRecords && $options.exportRecords(...args)),
    n: common_vendor.o((...args) => $options.confirmClearRecords && $options.confirmClearRecords(...args)),
    o: $options.filteredRecords.length > 0
  }, $options.filteredRecords.length > 0 ? {
    p: common_vendor.f($options.filteredRecords, (record, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(record.type === "read" ? "读取" : "写入"),
        b: common_vendor.n(record.type),
        c: common_vendor.t(record.success ? "✅" : "❌"),
        d: common_vendor.n(record.success ? "success" : "error"),
        e: common_vendor.t($options.getRecordPreview(record)),
        f: record.tagId
      }, record.tagId ? {
        g: common_vendor.t(record.tagId)
      } : {}, {
        h: common_vendor.t($options.formatDateTime(record.timestamp)),
        i: common_vendor.o(($event) => $options.shareRecord(record), record.id),
        j: common_vendor.o(($event) => $options.deleteRecord(record), record.id),
        k: record.id,
        l: common_vendor.o(($event) => $options.viewRecord(record), record.id),
        m: common_vendor.o(($event) => $options.showRecordActions(record), record.id)
      });
    })
  } : {
    q: common_vendor.t($options.getEmptyMessage()),
    r: common_vendor.o((...args) => $options.goToRead && $options.goToRead(...args))
  }, {
    s: $data.hasMore && $options.filteredRecords.length > 0
  }, $data.hasMore && $options.filteredRecords.length > 0 ? {
    t: common_vendor.t($data.isLoading ? "加载中..." : "加载更多"),
    v: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    w: $data.isLoading
  } : {}, {
    x: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/nfc-records/nfc-records.js.map

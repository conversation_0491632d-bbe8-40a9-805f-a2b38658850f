"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_nfc = require("../../utils/nfc.js");
const _sfc_main = common_vendor.defineComponent({
  data() {
    return {
      isScanning: false,
      tagFound: false,
      nfcAvailable: false,
      currentTag: null,
      ndefData: null,
      isReading: false,
      showRawData: false,
      errorMessage: "",
      recordId: null
      // 用于显示特定记录
    };
  },
  computed: {
    formattedNdefData() {
      if (!this.ndefData)
        return "";
      return utils_nfc.nfcManager.formatNFCData(this.ndefData);
    }
  },
  onLoad(options) {
    return common_vendor.__awaiter(this, void 0, void 0, function* () {
      if (options.recordId) {
        this.recordId = options.recordId;
        this.loadRecord(options.recordId);
      }
      yield this.checkNFCStatus();
      common_vendor.index.$on("nfc-tag-found", this.onTagFound);
    });
  },
  onUnload() {
    common_vendor.index.$off("nfc-tag-found", this.onTagFound);
    if (this.isScanning) {
      utils_nfc.nfcManager.stopDiscovery();
    }
  },
  methods: {
    checkNFCStatus() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        try {
          const status = yield utils_nfc.nfcManager.checkNFCAvailability();
          this.nfcAvailable = status.available;
          if (!status.available) {
            this.errorMessage = status.message;
          }
        } catch (error) {
          this.nfcAvailable = false;
          this.errorMessage = "检查NFC状态失败: " + error.message;
        }
      });
    },
    toggleScan() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        try {
          if (this.isScanning) {
            yield utils_nfc.nfcManager.stopDiscovery();
            this.isScanning = false;
            common_vendor.index.showToast({ title: "已停止扫描", icon: "success" });
          } else {
            yield utils_nfc.nfcManager.startDiscovery();
            this.isScanning = true;
            this.errorMessage = "";
            common_vendor.index.showToast({ title: "开始扫描NFC", icon: "success" });
          }
        } catch (error) {
          this.errorMessage = "操作失败: " + error.message;
          common_vendor.index.__f__("error", "at pages/nfc-read/nfc-read.uvue:166", "切换扫描状态失败:", error);
        }
      });
    },
    onTagFound(tag = null) {
      common_vendor.index.__f__("log", "at pages/nfc-read/nfc-read.uvue:171", "读取页面收到NFC标签:", tag);
      this.currentTag = tag;
      this.tagFound = true;
      this.errorMessage = "";
      this.readNDEF();
    },
    readNDEF() {
      return common_vendor.__awaiter(this, void 0, void 0, function* () {
        if (!this.currentTag)
          return Promise.resolve(null);
        this.isReading = true;
        this.errorMessage = "";
        try {
          const result = yield utils_nfc.nfcManager.readNDEF(this.currentTag.tagId);
          this.ndefData = result;
          common_vendor.index.showToast({ title: "读取成功", icon: "success" });
        } catch (error) {
          this.errorMessage = "读取NDEF失败: " + error.message;
          common_vendor.index.__f__("error", "at pages/nfc-read/nfc-read.uvue:192", "读取NDEF失败:", error);
        } finally {
          this.isReading = false;
        }
      });
    },
    loadRecord(recordId = null) {
      const records = utils_nfc.nfcManager.getRecords();
      const record = records.find((r = null) => {
        return r.id === recordId;
      });
      if (record) {
        this.currentTag = record;
        this.tagFound = true;
        if (record.messages) {
          this.ndefData = { messages: record.messages };
        }
      }
    },
    copyData() {
      if (!this.formattedNdefData)
        return null;
      common_vendor.index.setClipboardData({
        data: this.formattedNdefData,
        success: () => {
          common_vendor.index.showToast({ title: "已复制到剪贴板", icon: "success" });
        }
      });
    },
    shareData() {
      if (!this.formattedNdefData)
        return null;
      common_vendor.index.share(new UTSJSONObject({
        provider: "weixin",
        type: 0,
        title: "NFC数据分享",
        summary: this.formattedNdefData,
        success: () => {
          common_vendor.index.showToast({ title: "分享成功", icon: "success" });
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/nfc-read/nfc-read.uvue:233", "分享失败:", error);
          common_vendor.index.showToast({ title: "分享失败", icon: "error" });
        }
      }));
    },
    toggleRawData() {
      this.showRawData = !this.showRawData;
    },
    saveRecord() {
      if (!this.currentTag)
        return null;
      common_vendor.index.showToast({ title: "记录已保存", icon: "success" });
    },
    clearData() {
      this.currentTag = null;
      this.ndefData = null;
      this.tagFound = false;
      this.showRawData = false;
      this.errorMessage = "";
      common_vendor.index.showToast({ title: "数据已清除", icon: "success" });
    },
    formatDateTime(timestamp = null) {
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}:${date.getSeconds().toString().padStart(2, "0")}`;
    }
  }
});
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.tagFound ? "✅" : $data.isScanning ? "📡" : "📱"),
    b: $data.isScanning ? 1 : "",
    c: $data.tagFound ? 1 : "",
    d: common_vendor.t($data.tagFound ? "已发现标签" : $data.isScanning ? "正在扫描..." : "点击开始扫描"),
    e: common_vendor.t($data.isScanning ? "停止扫描" : "开始扫描"),
    f: common_vendor.o((...args) => $options.toggleScan && $options.toggleScan(...args)),
    g: !$data.nfcAvailable,
    h: $data.tagFound
  }, $data.tagFound ? {
    i: common_vendor.t($data.isReading ? "读取中..." : "读取NDEF数据"),
    j: common_vendor.o((...args) => $options.readNDEF && $options.readNDEF(...args)),
    k: !$data.tagFound || $data.isReading
  } : {}, {
    l: $data.currentTag
  }, $data.currentTag ? {
    m: common_vendor.t($data.currentTag.tagId),
    n: common_vendor.t($data.currentTag.techs ? $data.currentTag.techs.join(", ") : "未知"),
    o: common_vendor.t($options.formatDateTime($data.currentTag.timestamp))
  } : {}, {
    p: $data.ndefData
  }, $data.ndefData ? {
    q: common_vendor.t($options.formattedNdefData),
    r: common_vendor.o((...args) => $options.copyData && $options.copyData(...args)),
    s: common_vendor.o((...args) => $options.shareData && $options.shareData(...args))
  } : {}, {
    t: $data.currentTag && $data.showRawData
  }, $data.currentTag && $data.showRawData ? {
    v: common_vendor.t(JSON.stringify($data.currentTag.rawData, null, 2))
  } : {}, {
    w: $data.currentTag
  }, $data.currentTag ? {
    x: common_vendor.t($data.showRawData ? "隐藏" : "显示"),
    y: common_vendor.o((...args) => $options.toggleRawData && $options.toggleRawData(...args))
  } : {}, {
    z: $data.currentTag
  }, $data.currentTag ? {
    A: common_vendor.o((...args) => $options.saveRecord && $options.saveRecord(...args))
  } : {}, {
    B: common_vendor.o((...args) => $options.clearData && $options.clearData(...args)),
    C: $data.errorMessage
  }, $data.errorMessage ? {
    D: common_vendor.t($data.errorMessage)
  } : {}, {
    E: common_vendor.sei(common_vendor.gei(_ctx, ""), "view")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/nfc-read/nfc-read.js.map

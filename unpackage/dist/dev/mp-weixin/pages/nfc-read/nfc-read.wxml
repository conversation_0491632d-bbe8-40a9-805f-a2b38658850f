<view id="{{E}}" change:eS="{{uV.sS}}" eS="{{$eS[E]}}" change:eA="{{uV.sA}}" eA="{{$eA[E]}}" class="{{['container', virtualHostClass]}}" style="{{virtualHostStyle}}" hidden="{{virtualHostHidden || false}}"><view class="header"><text class="page-title">NFC读取</text><text class="page-subtitle">将设备靠近NFC标签进行读取</text></view><view class="scan-status"><view class="{{['scan-indicator', b && 'scanning', c && 'found']}}"><text class="scan-icon">{{a}}</text></view><text class="scan-text">{{d}}</text></view><view class="actions"><button class="action-btn primary" bindtap="{{f}}" disabled="{{g}}">{{e}}</button><button wx:if="{{h}}" class="action-btn secondary" bindtap="{{j}}" disabled="{{k}}">{{i}}</button></view><view wx:if="{{l}}" class="tag-info"><view class="info-section"><text class="section-title">标签信息</text><view class="info-item"><text class="info-label">标签ID:</text><text class="info-value">{{m}}</text></view><view class="info-item"><text class="info-label">技术类型:</text><text class="info-value">{{n}}</text></view><view class="info-item"><text class="info-label">发现时间:</text><text class="info-value">{{o}}</text></view></view></view><view wx:if="{{p}}" class="ndef-data"><view class="info-section"><text class="section-title">NDEF数据</text><view class="data-content"><text class="data-text">{{q}}</text></view><view class="data-actions"><button class="mini-btn" bindtap="{{r}}">复制数据</button><button class="mini-btn" bindtap="{{s}}">分享数据</button></view></view></view><view wx:if="{{t}}" class="raw-data"><view class="info-section"><text class="section-title">原始数据</text><view class="data-content"><text class="data-text raw">{{v}}</text></view></view></view><view class="bottom-actions"><button wx:if="{{w}}" class="bottom-btn" bindtap="{{y}}">{{x}}原始数据 </button><button wx:if="{{z}}" class="bottom-btn" bindtap="{{A}}"> 保存记录 </button><button class="bottom-btn" bindtap="{{B}}"> 清除数据 </button></view><view wx:if="{{C}}" class="error-message"><text class="error-text">{{D}}</text></view></view><wxs src="/common/uniView.wxs" module="uV"/>

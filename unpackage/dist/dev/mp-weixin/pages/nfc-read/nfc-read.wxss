@import "../../uvue.wxss";
:host{display:flex;flex-direction:column}

.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
}
.header {
		text-align: center;
		margin-bottom: 30px;
}
.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8px;
}
.page-subtitle {
		font-size: 14px;
		color: #666;
}

	/* 扫描状态 */
.scan-status {
		text-align: center;
		margin-bottom: 30px;
}
.scan-indicator {
		width: 120px;
		height: 120px;
		border-radius: 60px;
		background: #f0f0f0;
		margin: 0 auto 15px;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
}
.scan-indicator.scanning {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		animation: pulse 2s infinite;
}
.scan-indicator.found {
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}
@keyframes pulse {
0% { transform: scale(1);
}
50% { transform: scale(1.1);
}
100% { transform: scale(1);
}
}
.scan-icon {
		font-size: 40px;
		color: white;
}
.scan-text {
		font-size: 16px;
		color: #666;
		font-weight: bold;
}

	/* 操作按钮 */
.actions {
		margin-bottom: 30px;
}
.action-btn {
		width: 100%;
		height: 50px;
		border-radius: 12px;
		margin-bottom: 15px;
		border: none;
		font-size: 16px;
		font-weight: bold;
}
.action-btn:last-child {
		margin-bottom: 0;
}
.primary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
}
.secondary {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		color: white;
}
.action-btn[disabled] {
		background: #d9d9d9 !important;
		color: #999 !important;
}

	/* 信息区域 */
.tag-info, .ndef-data, .raw-data {
		margin-bottom: 20px;
}
.info-section {
		background: white;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
		display: block;
}
.info-item {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 12px;
		padding-bottom: 12px;
		border-bottom: 1px solid #f0f0f0;
}
.info-item:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
}
.info-label {
		font-size: 14px;
		color: #666;
		flex-shrink: 0;
		margin-right: 15px;
}
.info-value {
		font-size: 14px;
		color: #333;
		text-align: right;
		word-break: break-all;
}

	/* 数据内容 */
.data-content {
		background: #f8f9fa;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 15px;
}
.data-text {
		font-size: 14px;
		color: #333;
		line-height: 1.6;
		word-break: break-all;
}
.data-text.raw {
		font-family: monospace;
		font-size: 12px;
		color: #666;
}
.data-actions {
		display: flex;
		gap: 10px;
}
.mini-btn {
		flex: 1;
		height: 35px;
		border-radius: 6px;
		border: 1px solid #d9d9d9;
		background: white;
		font-size: 14px;
		color: #666;
}

	/* 底部操作 */
.bottom-actions {
		display: flex;
		flex-wrap: wrap;
		gap: 10px;
		margin-top: 30px;
}
.bottom-btn {
		flex: 1;
		min-width: 100px;
		height: 40px;
		border-radius: 8px;
		border: 1px solid #d9d9d9;
		background: white;
		font-size: 14px;
		color: #666;
}

	/* 错误提示 */
.error-message {
		background: #fff2f0;
		border: 1px solid #ffccc7;
		border-radius: 8px;
		padding: 15px;
		margin-top: 20px;
}
.error-text {
		font-size: 14px;
		color: #ff4d4f;
		line-height: 1.4;
}

{"version": 3, "file": "nfc.js", "sources": ["utils/nfc.js"], "sourcesContent": ["/**\n * NFC工具类\n * 封装NFC的读取、写入、记录等核心功能\n */\nclass NFCManager {\n  constructor() {\n    this.nfcAdapter = null;\n    this.isNFCAvailable = false;\n    this.isDiscovering = false;\n    this.records = []; // 存储NFC操作记录\n    this.init();\n  }\n\n  /**\n   * 初始化NFC适配器\n   */\n  async init() {\n    try {\n      // 检查是否支持NFC\n      if (!uni.getNFCAdapter) {\n        throw new Error('当前环境不支持NFC功能');\n      }\n\n      // 获取NFC适配器\n      this.nfcAdapter = uni.getNFCAdapter();\n\n      // 监听NFC适配器状态变化\n      this.nfcAdapter.onAdapterStateChange((res) => {\n        console.log('NFC适配器状态变化:', res);\n        this.isNFCAvailable = res.available;\n        this.isDiscovering = res.discovering;\n      });\n\n      // 监听NFC标签发现\n      this.nfcAdapter.onTagFound((tag) => {\n        console.log('发现NFC标签:', tag);\n        this.handleTagFound(tag);\n      });\n\n      // 获取初始状态\n      try {\n        const state = await this.nfcAdapter.getAdapterState();\n        this.isNFCAvailable = state.available;\n        this.isDiscovering = state.discovering;\n        console.log('NFC适配器初始化成功:', state);\n      } catch (stateError) {\n        console.warn('获取NFC状态失败，使用默认值:', stateError);\n        // 在某些情况下可能无法获取状态，设置默认值\n        this.isNFCAvailable = true;\n        this.isDiscovering = false;\n      }\n\n    } catch (error) {\n      console.error('NFC适配器初始化失败:', error);\n      this.isNFCAvailable = false;\n    }\n  }\n\n  /**\n   * 检查NFC是否可用\n   */\n  async checkNFCAvailability() {\n    if (!this.nfcAdapter) {\n      return { available: false, message: 'NFC适配器未初始化' };\n    }\n\n    try {\n      const state = await this.nfcAdapter.getAdapterState();\n      return {\n        available: state.available,\n        discovering: state.discovering,\n        message: state.available ? 'NFC可用' : 'NFC不可用，请检查设备设置'\n      };\n    } catch (error) {\n      return { available: false, message: '检查NFC状态失败: ' + error.message };\n    }\n  }\n\n  /**\n   * 开始NFC发现\n   */\n  async startDiscovery() {\n    if (!this.nfcAdapter) {\n      throw new Error('NFC适配器未初始化');\n    }\n\n    try {\n      // 检查NFC状态\n      const status = await this.checkNFCAvailability();\n      if (!status.available) {\n        throw new Error(status.message);\n      }\n\n      await this.nfcAdapter.startDiscovery({\n        success: () => {\n          this.isDiscovering = true;\n          console.log('开始NFC发现成功');\n        },\n        fail: (error) => {\n          console.error('启动NFC发现失败:', error);\n          throw new Error('启动NFC发现失败: ' + (error.errMsg || error.message));\n        }\n      });\n\n    } catch (error) {\n      console.error('启动NFC发现失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 停止NFC发现\n   */\n  async stopDiscovery() {\n    if (!this.nfcAdapter) {\n      return;\n    }\n\n    try {\n      await this.nfcAdapter.stopDiscovery({\n        success: () => {\n          this.isDiscovering = false;\n          console.log('停止NFC发现成功');\n        },\n        fail: (error) => {\n          console.error('停止NFC发现失败:', error);\n        }\n      });\n    } catch (error) {\n      console.error('停止NFC发现失败:', error);\n    }\n  }\n\n  /**\n   * 处理发现的NFC标签\n   */\n  handleTagFound(tagData) {\n    const record = {\n      id: Date.now().toString(),\n      type: 'read',\n      timestamp: new Date(),\n      tagId: tagData.tagId,\n      techs: tagData.techs,\n      messages: tagData.messages || [],\n      rawData: tagData\n    };\n\n    this.records.unshift(record);\n    \n    // 触发自定义事件\n    uni.$emit('nfc-tag-found', record);\n  }\n\n  /**\n   * 读取NDEF数据\n   */\n  async readNDEF(tagId) {\n    if (!this.nfcAdapter) {\n      throw new Error('NFC适配器未初始化');\n    }\n\n    try {\n      const ndef = this.nfcAdapter.getNdef();\n\n      return new Promise((resolve, reject) => {\n        ndef.readNdefMessage({\n          success: (result) => {\n            const record = {\n              id: Date.now().toString(),\n              type: 'read',\n              timestamp: new Date(),\n              tagId: tagId,\n              messages: result.messages,\n              success: true\n            };\n\n            this.records.unshift(record);\n            resolve(result);\n          },\n          fail: (error) => {\n            const record = {\n              id: Date.now().toString(),\n              type: 'read',\n              timestamp: new Date(),\n              tagId: tagId,\n              error: error.errMsg || error.message,\n              success: false\n            };\n\n            this.records.unshift(record);\n            reject(new Error(error.errMsg || error.message || '读取NDEF失败'));\n          }\n        });\n      });\n    } catch (error) {\n      const record = {\n        id: Date.now().toString(),\n        type: 'read',\n        timestamp: new Date(),\n        tagId: tagId,\n        error: error.message,\n        success: false\n      };\n\n      this.records.unshift(record);\n      throw error;\n    }\n  }\n\n  /**\n   * 写入NDEF数据\n   */\n  async writeNDEF(data, tagId) {\n    if (!this.nfcAdapter) {\n      throw new Error('NFC适配器未初始化');\n    }\n\n    try {\n      const ndef = this.nfcAdapter.getNdef();\n\n      // 构造NDEF消息\n      const message = {\n        records: [{\n          id: new ArrayBuffer(0),\n          type: new TextEncoder().encode('T').buffer,\n          payload: new TextEncoder().encode(data).buffer\n        }]\n      };\n\n      return new Promise((resolve, reject) => {\n        ndef.writeNdefMessage({\n          messages: [message],\n          success: () => {\n            const record = {\n              id: Date.now().toString(),\n              type: 'write',\n              timestamp: new Date(),\n              tagId: tagId,\n              data: data,\n              success: true\n            };\n\n            this.records.unshift(record);\n            resolve({ success: true, message: '写入成功' });\n          },\n          fail: (error) => {\n            const record = {\n              id: Date.now().toString(),\n              type: 'write',\n              timestamp: new Date(),\n              tagId: tagId,\n              data: data,\n              error: error.errMsg || error.message,\n              success: false\n            };\n\n            this.records.unshift(record);\n            reject(new Error(error.errMsg || error.message || '写入NDEF失败'));\n          }\n        });\n      });\n    } catch (error) {\n      const record = {\n        id: Date.now().toString(),\n        type: 'write',\n        timestamp: new Date(),\n        tagId: tagId,\n        data: data,\n        error: error.message,\n        success: false\n      };\n\n      this.records.unshift(record);\n      throw error;\n    }\n  }\n\n  /**\n   * 获取操作记录\n   */\n  getRecords(limit = 50) {\n    return this.records.slice(0, limit);\n  }\n\n  /**\n   * 清除记录\n   */\n  clearRecords() {\n    this.records = [];\n  }\n\n  /**\n   * 删除指定记录\n   */\n  deleteRecord(recordId) {\n    const index = this.records.findIndex(record => record.id === recordId);\n    if (index > -1) {\n      this.records.splice(index, 1);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * 导出记录为JSON\n   */\n  exportRecords() {\n    return JSON.stringify(this.records, null, 2);\n  }\n\n  /**\n   * 格式化NFC数据显示\n   */\n  formatNFCData(data) {\n    if (!data) return '无数据';\n\n    if (typeof data === 'string') {\n      return data;\n    }\n\n    // 处理微信小程序NDEF消息格式\n    if (Array.isArray(data)) {\n      // data是messages数组\n      return data.map(message => {\n        if (message.records && Array.isArray(message.records)) {\n          return message.records.map(record => {\n            if (record.payload) {\n              try {\n                // 微信小程序中payload是ArrayBuffer\n                const decoder = new TextDecoder('utf-8');\n                return decoder.decode(record.payload);\n              } catch (e) {\n                return '二进制数据';\n              }\n            }\n            return '未知格式';\n          }).join('\\n');\n        }\n        return '无记录';\n      }).join('\\n---\\n');\n    }\n\n    // 处理单个message对象\n    if (data.records && Array.isArray(data.records)) {\n      return data.records.map(record => {\n        if (record.payload) {\n          try {\n            const decoder = new TextDecoder('utf-8');\n            return decoder.decode(record.payload);\n          } catch (e) {\n            return '二进制数据';\n          }\n        }\n        return '未知格式';\n      }).join('\\n');\n    }\n\n    return JSON.stringify(data, null, 2);\n  }\n}\n\n// 创建全局实例\nconst nfcManager = new NFCManager();\n\nexport default nfcManager;\n"], "names": ["uni"], "mappings": ";;AAIA,MAAM,WAAW;AAAA,EACf,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,KAAI;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,OAAO;AACX,QAAI;AAEF,UAAI,CAACA,cAAG,MAAC,eAAe;AACtB,cAAM,IAAI,MAAM,cAAc;AAAA,MAC/B;AAGD,WAAK,aAAaA,oBAAI;AAGtB,WAAK,WAAW,qBAAqB,CAAC,QAAQ;AAC5CA,sBAAY,MAAA,MAAA,OAAA,sBAAA,eAAe,GAAG;AAC9B,aAAK,iBAAiB,IAAI;AAC1B,aAAK,gBAAgB,IAAI;AAAA,MACjC,CAAO;AAGD,WAAK,WAAW,WAAW,CAAC,QAAQ;AAClCA,sBAAA,MAAA,MAAA,OAAA,sBAAY,YAAY,GAAG;AAC3B,aAAK,eAAe,GAAG;AAAA,MAC/B,CAAO;AAGD,UAAI;AACF,cAAM,QAAQ,MAAM,KAAK,WAAW,gBAAe;AACnD,aAAK,iBAAiB,MAAM;AAC5B,aAAK,gBAAgB,MAAM;AAC3BA,sBAAA,MAAA,MAAA,OAAA,sBAAY,gBAAgB,KAAK;AAAA,MAClC,SAAQ,YAAY;AACnBA,sBAAA,MAAA,MAAA,QAAA,sBAAa,oBAAoB,UAAU;AAE3C,aAAK,iBAAiB;AACtB,aAAK,gBAAgB;AAAA,MACtB;AAAA,IAEF,SAAQ,OAAO;AACdA,oBAAA,MAAA,MAAA,SAAA,sBAAc,gBAAgB,KAAK;AACnC,WAAK,iBAAiB;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,uBAAuB;AAC3B,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO,EAAE,WAAW,OAAO,SAAS,aAAY;AAAA,IACjD;AAED,QAAI;AACF,YAAM,QAAQ,MAAM,KAAK,WAAW,gBAAe;AACnD,aAAO;AAAA,QACL,WAAW,MAAM;AAAA,QACjB,aAAa,MAAM;AAAA,QACnB,SAAS,MAAM,YAAY,UAAU;AAAA,MAC7C;AAAA,IACK,SAAQ,OAAO;AACd,aAAO,EAAE,WAAW,OAAO,SAAS,gBAAgB,MAAM;IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,iBAAiB;AACrB,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,YAAY;AAAA,IAC7B;AAED,QAAI;AAEF,YAAM,SAAS,MAAM,KAAK;AAC1B,UAAI,CAAC,OAAO,WAAW;AACrB,cAAM,IAAI,MAAM,OAAO,OAAO;AAAA,MAC/B;AAED,YAAM,KAAK,WAAW,eAAe;AAAA,QACnC,SAAS,MAAM;AACb,eAAK,gBAAgB;AACrBA,wBAAAA,MAAY,MAAA,OAAA,sBAAA,WAAW;AAAA,QACxB;AAAA,QACD,MAAM,CAAC,UAAU;AACfA,wBAAA,MAAA,MAAA,SAAA,uBAAc,cAAc,KAAK;AACjC,gBAAM,IAAI,MAAM,iBAAiB,MAAM,UAAU,MAAM,QAAQ;AAAA,QAChE;AAAA,MACT,CAAO;AAAA,IAEF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,uBAAA,cAAc,KAAK;AACjC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,gBAAgB;AACpB,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACD;AAED,QAAI;AACF,YAAM,KAAK,WAAW,cAAc;AAAA,QAClC,SAAS,MAAM;AACb,eAAK,gBAAgB;AACrBA,wBAAAA,MAAY,MAAA,OAAA,uBAAA,WAAW;AAAA,QACxB;AAAA,QACD,MAAM,CAAC,UAAU;AACfA,wBAAA,MAAA,MAAA,SAAA,uBAAc,cAAc,KAAK;AAAA,QAClC;AAAA,MACT,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,uBAAA,cAAc,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,SAAS;AACtB,UAAM,SAAS;AAAA,MACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,MACzB,MAAM;AAAA,MACN,WAAW,oBAAI,KAAM;AAAA,MACrB,OAAO,QAAQ;AAAA,MACf,OAAO,QAAQ;AAAA,MACf,UAAU,QAAQ,YAAY,CAAE;AAAA,MAChC,SAAS;AAAA,IACf;AAEI,SAAK,QAAQ,QAAQ,MAAM;AAG3BA,kBAAAA,MAAI,MAAM,iBAAiB,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,SAAS,OAAO;AACpB,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,YAAY;AAAA,IAC7B;AAED,QAAI;AACF,YAAM,OAAO,KAAK,WAAW,QAAO;AAEpC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAK,gBAAgB;AAAA,UACnB,SAAS,CAAC,WAAW;AACnB,kBAAM,SAAS;AAAA,cACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,cACzB,MAAM;AAAA,cACN,WAAW,oBAAI,KAAM;AAAA,cACrB;AAAA,cACA,UAAU,OAAO;AAAA,cACjB,SAAS;AAAA,YACvB;AAEY,iBAAK,QAAQ,QAAQ,MAAM;AAC3B,oBAAQ,MAAM;AAAA,UACf;AAAA,UACD,MAAM,CAAC,UAAU;AACf,kBAAM,SAAS;AAAA,cACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,cACzB,MAAM;AAAA,cACN,WAAW,oBAAI,KAAM;AAAA,cACrB;AAAA,cACA,OAAO,MAAM,UAAU,MAAM;AAAA,cAC7B,SAAS;AAAA,YACvB;AAEY,iBAAK,QAAQ,QAAQ,MAAM;AAC3B,mBAAO,IAAI,MAAM,MAAM,UAAU,MAAM,WAAW,UAAU,CAAC;AAAA,UAC9D;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACF,SAAQ,OAAO;AACd,YAAM,SAAS;AAAA,QACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,QACzB,MAAM;AAAA,QACN,WAAW,oBAAI,KAAM;AAAA,QACrB;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS;AAAA,MACjB;AAEM,WAAK,QAAQ,QAAQ,MAAM;AAC3B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,UAAU,MAAM,OAAO;AAC3B,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,YAAY;AAAA,IAC7B;AAED,QAAI;AACF,YAAM,OAAO,KAAK,WAAW,QAAO;AAGpC,YAAM,UAAU;AAAA,QACd,SAAS,CAAC;AAAA,UACR,IAAI,IAAI,YAAY,CAAC;AAAA,UACrB,MAAM,IAAI,YAAW,EAAG,OAAO,GAAG,EAAE;AAAA,UACpC,SAAS,IAAI,YAAW,EAAG,OAAO,IAAI,EAAE;AAAA,QAClD,CAAS;AAAA,MACT;AAEM,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAK,iBAAiB;AAAA,UACpB,UAAU,CAAC,OAAO;AAAA,UAClB,SAAS,MAAM;AACb,kBAAM,SAAS;AAAA,cACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,cACzB,MAAM;AAAA,cACN,WAAW,oBAAI,KAAM;AAAA,cACrB;AAAA,cACA;AAAA,cACA,SAAS;AAAA,YACvB;AAEY,iBAAK,QAAQ,QAAQ,MAAM;AAC3B,oBAAQ,EAAE,SAAS,MAAM,SAAS,OAAQ,CAAA;AAAA,UAC3C;AAAA,UACD,MAAM,CAAC,UAAU;AACf,kBAAM,SAAS;AAAA,cACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,cACzB,MAAM;AAAA,cACN,WAAW,oBAAI,KAAM;AAAA,cACrB;AAAA,cACA;AAAA,cACA,OAAO,MAAM,UAAU,MAAM;AAAA,cAC7B,SAAS;AAAA,YACvB;AAEY,iBAAK,QAAQ,QAAQ,MAAM;AAC3B,mBAAO,IAAI,MAAM,MAAM,UAAU,MAAM,WAAW,UAAU,CAAC;AAAA,UAC9D;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACF,SAAQ,OAAO;AACd,YAAM,SAAS;AAAA,QACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,QACzB,MAAM;AAAA,QACN,WAAW,oBAAI,KAAM;AAAA,QACrB;AAAA,QACA;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS;AAAA,MACjB;AAEM,WAAK,QAAQ,QAAQ,MAAM;AAC3B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,WAAW,QAAQ,IAAI;AACrB,WAAO,KAAK,QAAQ,MAAM,GAAG,KAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACb,SAAK,UAAU;EAChB;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa,UAAU;AACrB,UAAM,QAAQ,KAAK,QAAQ,UAAU,YAAU,OAAO,OAAO,QAAQ;AACrE,QAAI,QAAQ,IAAI;AACd,WAAK,QAAQ,OAAO,OAAO,CAAC;AAC5B,aAAO;AAAA,IACR;AACD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB;AACd,WAAO,KAAK,UAAU,KAAK,SAAS,MAAM,CAAC;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc,MAAM;AAClB,QAAI,CAAC;AAAM,aAAO;AAElB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO;AAAA,IACR;AAGD,QAAI,MAAM,QAAQ,IAAI,GAAG;AAEvB,aAAO,KAAK,IAAI,aAAW;AACzB,YAAI,QAAQ,WAAW,MAAM,QAAQ,QAAQ,OAAO,GAAG;AACrD,iBAAO,QAAQ,QAAQ,IAAI,YAAU;AACnC,gBAAI,OAAO,SAAS;AAClB,kBAAI;AAEF,sBAAM,UAAU,IAAI,YAAY,OAAO;AACvC,uBAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,cACrC,SAAQ,GAAG;AACV,uBAAO;AAAA,cACR;AAAA,YACF;AACD,mBAAO;AAAA,UACnB,CAAW,EAAE,KAAK,IAAI;AAAA,QACb;AACD,eAAO;AAAA,MACf,CAAO,EAAE,KAAK,SAAS;AAAA,IAClB;AAGD,QAAI,KAAK,WAAW,MAAM,QAAQ,KAAK,OAAO,GAAG;AAC/C,aAAO,KAAK,QAAQ,IAAI,YAAU;AAChC,YAAI,OAAO,SAAS;AAClB,cAAI;AACF,kBAAM,UAAU,IAAI,YAAY,OAAO;AACvC,mBAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,UACrC,SAAQ,GAAG;AACV,mBAAO;AAAA,UACR;AAAA,QACF;AACD,eAAO;AAAA,MACf,CAAO,EAAE,KAAK,IAAI;AAAA,IACb;AAED,WAAO,KAAK,UAAU,MAAM,MAAM,CAAC;AAAA,EACpC;AACH;AAGK,MAAC,aAAa,IAAI,WAAU;;"}
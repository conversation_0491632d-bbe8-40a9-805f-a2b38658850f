{"version": 3, "file": "nfc.js", "sources": ["utils/nfc.js"], "sourcesContent": ["/**\n * NFC工具类\n * 封装NFC的读取、写入、记录等核心功能\n */\nclass NFCManager {\n  constructor() {\n    this.nfcAdapter = null;\n    this.isNFCAvailable = false;\n    this.isDiscovering = false;\n    this.records = []; // 存储NFC操作记录\n    this.init();\n  }\n\n  /**\n   * 初始化NFC适配器\n   */\n  async init() {\n    try {\n      // 获取NFC适配器\n      this.nfcAdapter = uni.getNFCAdapter();\n      this.isNFCAvailable = true;\n      \n      // 监听NFC适配器状态变化\n      this.nfcAdapter.onAdapterStateChange((res) => {\n        console.log('NFC适配器状态变化:', res);\n        this.isNFCAvailable = res.available;\n      });\n\n      console.log('NFC适配器初始化成功');\n    } catch (error) {\n      console.error('NFC适配器初始化失败:', error);\n      this.isNFCAvailable = false;\n    }\n  }\n\n  /**\n   * 检查NFC是否可用\n   */\n  async checkNFCAvailability() {\n    if (!this.nfcAdapter) {\n      return { available: false, message: 'NFC适配器未初始化' };\n    }\n\n    try {\n      const state = await this.nfcAdapter.getAdapterState();\n      return {\n        available: state.available,\n        discovering: state.discovering,\n        message: state.available ? 'NFC可用' : 'NFC不可用，请检查设备设置'\n      };\n    } catch (error) {\n      return { available: false, message: '检查NFC状态失败: ' + error.message };\n    }\n  }\n\n  /**\n   * 开始NFC发现\n   */\n  async startDiscovery() {\n    if (!this.nfcAdapter || !this.isNFCAvailable) {\n      throw new Error('NFC不可用');\n    }\n\n    try {\n      await this.nfcAdapter.startDiscovery();\n      this.isDiscovering = true;\n      console.log('开始NFC发现');\n      \n      // 监听NFC标签发现\n      this.nfcAdapter.onTagFound((res) => {\n        console.log('发现NFC标签:', res);\n        this.handleTagFound(res);\n      });\n\n    } catch (error) {\n      console.error('启动NFC发现失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 停止NFC发现\n   */\n  async stopDiscovery() {\n    if (!this.nfcAdapter) {\n      return;\n    }\n\n    try {\n      await this.nfcAdapter.stopDiscovery();\n      this.isDiscovering = false;\n      console.log('停止NFC发现');\n    } catch (error) {\n      console.error('停止NFC发现失败:', error);\n    }\n  }\n\n  /**\n   * 处理发现的NFC标签\n   */\n  handleTagFound(tagData) {\n    const record = {\n      id: Date.now().toString(),\n      type: 'read',\n      timestamp: new Date(),\n      tagId: tagData.tagId,\n      techs: tagData.techs,\n      messages: tagData.messages || [],\n      rawData: tagData\n    };\n\n    this.records.unshift(record);\n    \n    // 触发自定义事件\n    uni.$emit('nfc-tag-found', record);\n  }\n\n  /**\n   * 读取NDEF数据\n   */\n  async readNDEF(tagId) {\n    if (!this.nfcAdapter) {\n      throw new Error('NFC适配器未初始化');\n    }\n\n    try {\n      const ndef = this.nfcAdapter.getNdef();\n      const result = await ndef.readNdefMessage();\n      \n      const record = {\n        id: Date.now().toString(),\n        type: 'read',\n        timestamp: new Date(),\n        tagId: tagId,\n        data: result.messages,\n        success: true\n      };\n\n      this.records.unshift(record);\n      return result;\n    } catch (error) {\n      const record = {\n        id: Date.now().toString(),\n        type: 'read',\n        timestamp: new Date(),\n        tagId: tagId,\n        error: error.message,\n        success: false\n      };\n\n      this.records.unshift(record);\n      throw error;\n    }\n  }\n\n  /**\n   * 写入NDEF数据\n   */\n  async writeNDEF(data, tagId) {\n    if (!this.nfcAdapter) {\n      throw new Error('NFC适配器未初始化');\n    }\n\n    try {\n      const ndef = this.nfcAdapter.getNdef();\n      \n      // 构造NDEF消息\n      const message = {\n        records: [{\n          id: new ArrayBuffer(0),\n          type: new TextEncoder().encode('T').buffer,\n          payload: new TextEncoder().encode(data).buffer\n        }]\n      };\n\n      await ndef.writeNdefMessage(message);\n      \n      const record = {\n        id: Date.now().toString(),\n        type: 'write',\n        timestamp: new Date(),\n        tagId: tagId,\n        data: data,\n        success: true\n      };\n\n      this.records.unshift(record);\n      return { success: true, message: '写入成功' };\n    } catch (error) {\n      const record = {\n        id: Date.now().toString(),\n        type: 'write',\n        timestamp: new Date(),\n        tagId: tagId,\n        data: data,\n        error: error.message,\n        success: false\n      };\n\n      this.records.unshift(record);\n      throw error;\n    }\n  }\n\n  /**\n   * 获取操作记录\n   */\n  getRecords(limit = 50) {\n    return this.records.slice(0, limit);\n  }\n\n  /**\n   * 清除记录\n   */\n  clearRecords() {\n    this.records = [];\n  }\n\n  /**\n   * 删除指定记录\n   */\n  deleteRecord(recordId) {\n    const index = this.records.findIndex(record => record.id === recordId);\n    if (index > -1) {\n      this.records.splice(index, 1);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * 导出记录为JSON\n   */\n  exportRecords() {\n    return JSON.stringify(this.records, null, 2);\n  }\n\n  /**\n   * 格式化NFC数据显示\n   */\n  formatNFCData(data) {\n    if (!data) return '无数据';\n    \n    if (typeof data === 'string') {\n      return data;\n    }\n    \n    if (data.records && Array.isArray(data.records)) {\n      return data.records.map(record => {\n        if (record.payload) {\n          try {\n            return new TextDecoder().decode(record.payload);\n          } catch (e) {\n            return '二进制数据';\n          }\n        }\n        return '未知格式';\n      }).join('\\n');\n    }\n    \n    return JSON.stringify(data, null, 2);\n  }\n}\n\n// 创建全局实例\nconst nfcManager = new NFCManager();\n\nexport default nfcManager;\n"], "names": ["uni"], "mappings": ";;AAIA,MAAM,WAAW;AAAA,EACf,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,KAAI;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,OAAO;AACX,QAAI;AAEF,WAAK,aAAaA,oBAAI;AACtB,WAAK,iBAAiB;AAGtB,WAAK,WAAW,qBAAqB,CAAC,QAAQ;AAC5CA,sBAAY,MAAA,MAAA,OAAA,sBAAA,eAAe,GAAG;AAC9B,aAAK,iBAAiB,IAAI;AAAA,MAClC,CAAO;AAEDA,oBAAAA,MAAY,MAAA,OAAA,sBAAA,aAAa;AAAA,IAC1B,SAAQ,OAAO;AACdA,oBAAA,MAAA,MAAA,SAAA,sBAAc,gBAAgB,KAAK;AACnC,WAAK,iBAAiB;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,uBAAuB;AAC3B,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO,EAAE,WAAW,OAAO,SAAS,aAAY;AAAA,IACjD;AAED,QAAI;AACF,YAAM,QAAQ,MAAM,KAAK,WAAW,gBAAe;AACnD,aAAO;AAAA,QACL,WAAW,MAAM;AAAA,QACjB,aAAa,MAAM;AAAA,QACnB,SAAS,MAAM,YAAY,UAAU;AAAA,MAC7C;AAAA,IACK,SAAQ,OAAO;AACd,aAAO,EAAE,WAAW,OAAO,SAAS,gBAAgB,MAAM;IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,iBAAiB;AACrB,QAAI,CAAC,KAAK,cAAc,CAAC,KAAK,gBAAgB;AAC5C,YAAM,IAAI,MAAM,QAAQ;AAAA,IACzB;AAED,QAAI;AACF,YAAM,KAAK,WAAW;AACtB,WAAK,gBAAgB;AACrBA,oBAAAA,yCAAY,SAAS;AAGrB,WAAK,WAAW,WAAW,CAAC,QAAQ;AAClCA,sBAAA,MAAA,MAAA,OAAA,sBAAY,YAAY,GAAG;AAC3B,aAAK,eAAe,GAAG;AAAA,MAC/B,CAAO;AAAA,IAEF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,sBAAA,cAAc,KAAK;AACjC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,gBAAgB;AACpB,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACD;AAED,QAAI;AACF,YAAM,KAAK,WAAW;AACtB,WAAK,gBAAgB;AACrBA,oBAAAA,yCAAY,SAAS;AAAA,IACtB,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,sBAAA,cAAc,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,SAAS;AACtB,UAAM,SAAS;AAAA,MACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,MACzB,MAAM;AAAA,MACN,WAAW,oBAAI,KAAM;AAAA,MACrB,OAAO,QAAQ;AAAA,MACf,OAAO,QAAQ;AAAA,MACf,UAAU,QAAQ,YAAY,CAAE;AAAA,MAChC,SAAS;AAAA,IACf;AAEI,SAAK,QAAQ,QAAQ,MAAM;AAG3BA,kBAAAA,MAAI,MAAM,iBAAiB,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,SAAS,OAAO;AACpB,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,YAAY;AAAA,IAC7B;AAED,QAAI;AACF,YAAM,OAAO,KAAK,WAAW,QAAO;AACpC,YAAM,SAAS,MAAM,KAAK;AAE1B,YAAM,SAAS;AAAA,QACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,QACzB,MAAM;AAAA,QACN,WAAW,oBAAI,KAAM;AAAA,QACrB;AAAA,QACA,MAAM,OAAO;AAAA,QACb,SAAS;AAAA,MACjB;AAEM,WAAK,QAAQ,QAAQ,MAAM;AAC3B,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,YAAM,SAAS;AAAA,QACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,QACzB,MAAM;AAAA,QACN,WAAW,oBAAI,KAAM;AAAA,QACrB;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS;AAAA,MACjB;AAEM,WAAK,QAAQ,QAAQ,MAAM;AAC3B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,UAAU,MAAM,OAAO;AAC3B,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,YAAY;AAAA,IAC7B;AAED,QAAI;AACF,YAAM,OAAO,KAAK,WAAW,QAAO;AAGpC,YAAM,UAAU;AAAA,QACd,SAAS,CAAC;AAAA,UACR,IAAI,IAAI,YAAY,CAAC;AAAA,UACrB,MAAM,IAAI,YAAW,EAAG,OAAO,GAAG,EAAE;AAAA,UACpC,SAAS,IAAI,YAAW,EAAG,OAAO,IAAI,EAAE;AAAA,QAClD,CAAS;AAAA,MACT;AAEM,YAAM,KAAK,iBAAiB,OAAO;AAEnC,YAAM,SAAS;AAAA,QACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,QACzB,MAAM;AAAA,QACN,WAAW,oBAAI,KAAM;AAAA,QACrB;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MACjB;AAEM,WAAK,QAAQ,QAAQ,MAAM;AAC3B,aAAO,EAAE,SAAS,MAAM,SAAS,OAAM;AAAA,IACxC,SAAQ,OAAO;AACd,YAAM,SAAS;AAAA,QACb,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,QACzB,MAAM;AAAA,QACN,WAAW,oBAAI,KAAM;AAAA,QACrB;AAAA,QACA;AAAA,QACA,OAAO,MAAM;AAAA,QACb,SAAS;AAAA,MACjB;AAEM,WAAK,QAAQ,QAAQ,MAAM;AAC3B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,WAAW,QAAQ,IAAI;AACrB,WAAO,KAAK,QAAQ,MAAM,GAAG,KAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACb,SAAK,UAAU;EAChB;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa,UAAU;AACrB,UAAM,QAAQ,KAAK,QAAQ,UAAU,YAAU,OAAO,OAAO,QAAQ;AACrE,QAAI,QAAQ,IAAI;AACd,WAAK,QAAQ,OAAO,OAAO,CAAC;AAC5B,aAAO;AAAA,IACR;AACD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB;AACd,WAAO,KAAK,UAAU,KAAK,SAAS,MAAM,CAAC;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc,MAAM;AAClB,QAAI,CAAC;AAAM,aAAO;AAElB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO;AAAA,IACR;AAED,QAAI,KAAK,WAAW,MAAM,QAAQ,KAAK,OAAO,GAAG;AAC/C,aAAO,KAAK,QAAQ,IAAI,YAAU;AAChC,YAAI,OAAO,SAAS;AAClB,cAAI;AACF,mBAAO,IAAI,YAAa,EAAC,OAAO,OAAO,OAAO;AAAA,UAC/C,SAAQ,GAAG;AACV,mBAAO;AAAA,UACR;AAAA,QACF;AACD,eAAO;AAAA,MACf,CAAO,EAAE,KAAK,IAAI;AAAA,IACb;AAED,WAAO,KAAK,UAAU,MAAM,MAAM,CAAC;AAAA,EACpC;AACH;AAGK,MAAC,aAAa,IAAI,WAAU;;"}
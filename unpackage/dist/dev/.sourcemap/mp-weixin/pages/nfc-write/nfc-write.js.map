{"version": 3, "file": "nfc-write.js", "sources": ["pages/nfc-write/nfc-write.uvue", "pages/nfc-write/nfc-write.uvue?type=page"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"page-title\">NFC写入</text>\n\t\t\t<text class=\"page-subtitle\">输入数据并写入到NFC标签</text>\n\t\t</view>\n\n\t\t<!-- 数据输入区域 -->\n\t\t<view class=\"input-section\">\n\t\t\t<view class=\"section-title\">输入数据</view>\n\t\t\t\n\t\t\t<!-- 数据类型选择 -->\n\t\t\t<view class=\"data-type-selector\">\n\t\t\t\t<view class=\"type-item\" \n\t\t\t\t\t  :class=\"{ 'active': dataType === 'text' }\" \n\t\t\t\t\t  @click=\"selectDataType('text')\">\n\t\t\t\t\t<text class=\"type-icon\">📝</text>\n\t\t\t\t\t<text class=\"type-label\">文本</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"type-item\" \n\t\t\t\t\t  :class=\"{ 'active': dataType === 'url' }\" \n\t\t\t\t\t  @click=\"selectDataType('url')\">\n\t\t\t\t\t<text class=\"type-icon\">🔗</text>\n\t\t\t\t\t<text class=\"type-label\">网址</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"type-item\" \n\t\t\t\t\t  :class=\"{ 'active': dataType === 'wifi' }\" \n\t\t\t\t\t  @click=\"selectDataType('wifi')\">\n\t\t\t\t\t<text class=\"type-icon\">📶</text>\n\t\t\t\t\t<text class=\"type-label\">WiFi</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"type-item\" \n\t\t\t\t\t  :class=\"{ 'active': dataType === 'contact' }\" \n\t\t\t\t\t  @click=\"selectDataType('contact')\">\n\t\t\t\t\t<text class=\"type-icon\">👤</text>\n\t\t\t\t\t<text class=\"type-label\">联系人</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 文本输入 -->\n\t\t\t<view class=\"input-area\" v-if=\"dataType === 'text'\">\n\t\t\t\t<textarea class=\"data-input\" \n\t\t\t\t\t\t  v-model=\"inputData.text\" \n\t\t\t\t\t\t  placeholder=\"请输入要写入的文本内容\"\n\t\t\t\t\t\t  :maxlength=\"500\">\n\t\t\t\t</textarea>\n\t\t\t\t<text class=\"char-count\">{{inputData.text.length}}/500</text>\n\t\t\t</view>\n\n\t\t\t<!-- URL输入 -->\n\t\t\t<view class=\"input-area\" v-if=\"dataType === 'url'\">\n\t\t\t\t<input class=\"data-input\" \n\t\t\t\t\t   v-model=\"inputData.url\" \n\t\t\t\t\t   placeholder=\"请输入网址，如：https://www.example.com\"\n\t\t\t\t\t   type=\"url\">\n\t\t\t</view>\n\n\t\t\t<!-- WiFi信息输入 -->\n\t\t\t<view class=\"input-area\" v-if=\"dataType === 'wifi'\">\n\t\t\t\t<input class=\"data-input\" \n\t\t\t\t\t   v-model=\"inputData.wifi.ssid\" \n\t\t\t\t\t   placeholder=\"WiFi名称(SSID)\"\n\t\t\t\t\t   style=\"margin-bottom: 15px;\">\n\t\t\t\t<input class=\"data-input\" \n\t\t\t\t\t   v-model=\"inputData.wifi.password\" \n\t\t\t\t\t   placeholder=\"WiFi密码\"\n\t\t\t\t\t   type=\"password\"\n\t\t\t\t\t   style=\"margin-bottom: 15px;\">\n\t\t\t\t<picker @change=\"onSecurityChange\" :value=\"securityIndex\" :range=\"securityTypes\">\n\t\t\t\t\t<view class=\"picker-input\">\n\t\t\t\t\t\t<text>加密类型: {{securityTypes[securityIndex]}}</text>\n\t\t\t\t\t\t<text class=\"picker-arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</picker>\n\t\t\t</view>\n\n\t\t\t<!-- 联系人信息输入 -->\n\t\t\t<view class=\"input-area\" v-if=\"dataType === 'contact'\">\n\t\t\t\t<input class=\"data-input\" \n\t\t\t\t\t   v-model=\"inputData.contact.name\" \n\t\t\t\t\t   placeholder=\"姓名\"\n\t\t\t\t\t   style=\"margin-bottom: 15px;\">\n\t\t\t\t<input class=\"data-input\" \n\t\t\t\t\t   v-model=\"inputData.contact.phone\" \n\t\t\t\t\t   placeholder=\"电话号码\"\n\t\t\t\t\t   type=\"tel\"\n\t\t\t\t\t   style=\"margin-bottom: 15px;\">\n\t\t\t\t<input class=\"data-input\" \n\t\t\t\t\t   v-model=\"inputData.contact.email\" \n\t\t\t\t\t   placeholder=\"邮箱地址\"\n\t\t\t\t\t   type=\"email\">\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 预览区域 -->\n\t\t<view class=\"preview-section\" v-if=\"previewData\">\n\t\t\t<view class=\"section-title\">数据预览</view>\n\t\t\t<view class=\"preview-content\">\n\t\t\t\t<text class=\"preview-text\">{{previewData}}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 扫描状态 -->\n\t\t<view class=\"scan-status\">\n\t\t\t<view class=\"scan-indicator\" :class=\"{ 'scanning': isScanning, 'found': tagFound }\">\n\t\t\t\t<text class=\"scan-icon\">{{tagFound ? '✅' : (isScanning ? '📡' : '📱')}}</text>\n\t\t\t</view>\n\t\t\t<text class=\"scan-text\">\n\t\t\t\t{{tagFound ? '已发现标签，可以写入' : (isScanning ? '请将标签靠近设备' : '点击开始扫描标签')}}\n\t\t\t</text>\n\t\t</view>\n\n\t\t<!-- 操作按钮 -->\n\t\t<view class=\"actions\">\n\t\t\t<button class=\"action-btn primary\" @click=\"toggleScan\" :disabled=\"!nfcAvailable\">\n\t\t\t\t{{isScanning ? '停止扫描' : '扫描标签'}}\n\t\t\t</button>\n\t\t\t\n\t\t\t<button class=\"action-btn secondary\" @click=\"writeData\" \n\t\t\t\t\t:disabled=\"!canWrite || isWriting\">\n\t\t\t\t{{isWriting ? '写入中...' : '写入数据'}}\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 快捷数据模板 -->\n\t\t<view class=\"templates-section\">\n\t\t\t<view class=\"section-title\">快捷模板</view>\n\t\t\t<view class=\"template-list\">\n\t\t\t\t<view class=\"template-item\" @click=\"useTemplate('hello')\">\n\t\t\t\t\t<text class=\"template-icon\">👋</text>\n\t\t\t\t\t<text class=\"template-label\">问候语</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"template-item\" @click=\"useTemplate('website')\">\n\t\t\t\t\t<text class=\"template-icon\">🌐</text>\n\t\t\t\t\t<text class=\"template-label\">官网</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"template-item\" @click=\"useTemplate('contact')\">\n\t\t\t\t\t<text class=\"template-icon\">📞</text>\n\t\t\t\t\t<text class=\"template-label\">联系方式</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 错误提示 -->\n\t\t<view class=\"error-message\" v-if=\"errorMessage\">\n\t\t\t<text class=\"error-text\">{{errorMessage}}</text>\n\t\t</view>\n\n\t\t<!-- 成功提示 -->\n\t\t<view class=\"success-message\" v-if=\"successMessage\">\n\t\t\t<text class=\"success-text\">{{successMessage}}</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\timport nfcManager from '@/utils/nfc.js'\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tdataType: 'text',\n\t\t\t\tinputData: {\n\t\t\t\t\ttext: '',\n\t\t\t\t\turl: '',\n\t\t\t\t\twifi: {\n\t\t\t\t\t\tssid: '',\n\t\t\t\t\t\tpassword: '',\n\t\t\t\t\t\tsecurity: 'WPA'\n\t\t\t\t\t},\n\t\t\t\t\tcontact: {\n\t\t\t\t\t\tname: '',\n\t\t\t\t\t\tphone: '',\n\t\t\t\t\t\temail: ''\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tsecurityTypes: ['WPA', 'WEP', 'Open'],\n\t\t\t\tsecurityIndex: 0,\n\t\t\t\tisScanning: false,\n\t\t\t\ttagFound: false,\n\t\t\t\tcurrentTag: null,\n\t\t\t\tnfcAvailable: false,\n\t\t\t\tisWriting: false,\n\t\t\t\terrorMessage: '',\n\t\t\t\tsuccessMessage: ''\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tpreviewData() {\n\t\t\t\tswitch (this.dataType) {\n\t\t\t\t\tcase 'text':\n\t\t\t\t\t\treturn this.inputData.text;\n\t\t\t\t\tcase 'url':\n\t\t\t\t\t\treturn this.inputData.url;\n\t\t\t\t\tcase 'wifi':\n\t\t\t\t\t\treturn `WiFi: ${this.inputData.wifi.ssid}\\n密码: ${this.inputData.wifi.password}\\n加密: ${this.securityTypes[this.securityIndex]}`;\n\t\t\t\t\tcase 'contact':\n\t\t\t\t\t\treturn `姓名: ${this.inputData.contact.name}\\n电话: ${this.inputData.contact.phone}\\n邮箱: ${this.inputData.contact.email}`;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t},\n\t\t\tcanWrite() {\n\t\t\t\treturn this.tagFound && this.previewData.trim() && this.nfcAvailable;\n\t\t\t}\n\t\t},\n\t\tasync onLoad() {\n\t\t\tawait this.checkNFCStatus();\n\t\t\t\n\t\t\t// 监听NFC标签发现\n\t\t\tuni.$on('nfc-tag-found', this.onTagFound);\n\t\t},\n\t\tonUnload() {\n\t\t\t// 清理事件监听\n\t\t\tuni.$off('nfc-tag-found', this.onTagFound);\n\t\t\t// 停止扫描\n\t\t\tif (this.isScanning) {\n\t\t\t\tnfcManager.stopDiscovery();\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tasync checkNFCStatus() {\n\t\t\t\ttry {\n\t\t\t\t\tconst status = await nfcManager.checkNFCAvailability();\n\t\t\t\t\tthis.nfcAvailable = status.available;\n\t\t\t\t\tif (!status.available) {\n\t\t\t\t\t\tthis.errorMessage = status.message;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.nfcAvailable = false;\n\t\t\t\t\tthis.errorMessage = '检查NFC状态失败: ' + error.message;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tselectDataType(type) {\n\t\t\t\tthis.dataType = type;\n\t\t\t\tthis.clearMessages();\n\t\t\t},\n\t\t\t\n\t\t\tonSecurityChange(e) {\n\t\t\t\tthis.securityIndex = e.detail.value;\n\t\t\t\tthis.inputData.wifi.security = this.securityTypes[this.securityIndex];\n\t\t\t},\n\t\t\t\n\t\t\tasync toggleScan() {\n\t\t\t\ttry {\n\t\t\t\t\tif (this.isScanning) {\n\t\t\t\t\t\tawait nfcManager.stopDiscovery();\n\t\t\t\t\t\tthis.isScanning = false;\n\t\t\t\t\t\tuni.showToast({ title: '已停止扫描', icon: 'success' });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait nfcManager.startDiscovery();\n\t\t\t\t\t\tthis.isScanning = true;\n\t\t\t\t\t\tthis.clearMessages();\n\t\t\t\t\t\tuni.showToast({ title: '开始扫描NFC', icon: 'success' });\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.errorMessage = '操作失败: ' + error.message;\n\t\t\t\t\tuni.__f__('error','at pages/nfc-write/nfc-write.uvue:259','切换扫描状态失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tonTagFound(tag) {\n\t\t\t\tuni.__f__('log','at pages/nfc-write/nfc-write.uvue:264','写入页面收到NFC标签:', tag);\n\t\t\t\tthis.currentTag = tag;\n\t\t\t\tthis.tagFound = true;\n\t\t\t\tthis.clearMessages();\n\t\t\t\t\n\t\t\t\tuni.showToast({ title: '发现NFC标签', icon: 'success' });\n\t\t\t},\n\t\t\t\n\t\t\tasync writeData() {\n\t\t\t\tif (!this.canWrite) return;\n\t\t\t\t\n\t\t\t\tthis.isWriting = true;\n\t\t\t\tthis.clearMessages();\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst dataToWrite = this.formatDataForWriting();\n\t\t\t\t\tawait nfcManager.writeNDEF(dataToWrite, this.currentTag.tagId);\n\t\t\t\t\t\n\t\t\t\t\tthis.successMessage = '数据写入成功！';\n\t\t\t\t\tuni.showToast({ title: '写入成功', icon: 'success' });\n\t\t\t\t\t\n\t\t\t\t\t// 3秒后清除成功消息\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.successMessage = '';\n\t\t\t\t\t}, 3000);\n\t\t\t\t\t\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.errorMessage = '写入失败: ' + error.message;\n\t\t\t\t\tuni.__f__('error','at pages/nfc-write/nfc-write.uvue:292','写入数据失败:', error);\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isWriting = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tformatDataForWriting() {\n\t\t\t\tswitch (this.dataType) {\n\t\t\t\t\tcase 'text':\n\t\t\t\t\t\treturn this.inputData.text;\n\t\t\t\t\tcase 'url':\n\t\t\t\t\t\treturn this.inputData.url;\n\t\t\t\t\tcase 'wifi':\n\t\t\t\t\t\treturn `WIFI:T:${this.inputData.wifi.security};S:${this.inputData.wifi.ssid};P:${this.inputData.wifi.password};;`;\n\t\t\t\t\tcase 'contact':\n\t\t\t\t\t\treturn `BEGIN:VCARD\\nVERSION:3.0\\nFN:${this.inputData.contact.name}\\nTEL:${this.inputData.contact.phone}\\nEMAIL:${this.inputData.contact.email}\\nEND:VCARD`;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tuseTemplate(templateType) {\n\t\t\t\tswitch (templateType) {\n\t\t\t\t\tcase 'hello':\n\t\t\t\t\t\tthis.dataType = 'text';\n\t\t\t\t\t\tthis.inputData.text = '你好！欢迎使用NFC功能！';\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'website':\n\t\t\t\t\t\tthis.dataType = 'url';\n\t\t\t\t\t\tthis.inputData.url = 'https://www.example.com';\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'contact':\n\t\t\t\t\t\tthis.dataType = 'contact';\n\t\t\t\t\t\tthis.inputData.contact = {\n\t\t\t\t\t\t\tname: '张三',\n\t\t\t\t\t\t\tphone: '13800138000',\n\t\t\t\t\t\t\temail: '<EMAIL>'\n\t\t\t\t\t\t};\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tthis.clearMessages();\n\t\t\t},\n\t\t\t\n\t\t\tclearMessages() {\n\t\t\t\tthis.errorMessage = '';\n\t\t\t\tthis.successMessage = '';\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tpadding: 20px;\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f8f8f8;\n\t}\n\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.page-title {\n\t\tfont-size: 24px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 8px;\n\t}\n\n\t.page-subtitle {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t/* 输入区域 */\n\t.input-section, .preview-section, .templates-section {\n\t\tbackground: white;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t\tmargin-bottom: 20px;\n\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\t}\n\n\t.section-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 15px;\n\t\tdisplay: block;\n\t}\n\n\t/* 数据类型选择器 */\n\t.data-type-selector {\n\t\tdisplay: flex;\n\t\tgap: 10px;\n\t\tmargin-bottom: 20px;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.type-item {\n\t\tflex: 1;\n\t\tmin-width: 70px;\n\t\theight: 60px;\n\t\tborder: 2px solid #f0f0f0;\n\t\tborder-radius: 8px;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: white;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.type-item.active {\n\t\tborder-color: #1890ff;\n\t\tbackground: #e6f7ff;\n\t}\n\n\t.type-icon {\n\t\tfont-size: 20px;\n\t\tmargin-bottom: 4px;\n\t}\n\n\t.type-label {\n\t\tfont-size: 12px;\n\t\tcolor: #666;\n\t}\n\n\t.type-item.active .type-label {\n\t\tcolor: #1890ff;\n\t\tfont-weight: bold;\n\t}\n\n\t/* 输入区域 */\n\t.input-area {\n\t\tmargin-bottom: 15px;\n\t}\n\n\t.data-input {\n\t\twidth: 100%;\n\t\tpadding: 12px 15px;\n\t\tborder: 1px solid #d9d9d9;\n\t\tborder-radius: 8px;\n\t\tfont-size: 16px;\n\t\tbackground: white;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.data-input:focus {\n\t\tborder-color: #1890ff;\n\t\toutline: none;\n\t}\n\n\ttextarea.data-input {\n\t\tmin-height: 100px;\n\t\tresize: vertical;\n\t}\n\n\t.char-count {\n\t\tfont-size: 12px;\n\t\tcolor: #999;\n\t\ttext-align: right;\n\t\tmargin-top: 5px;\n\t\tdisplay: block;\n\t}\n\n\t.picker-input {\n\t\tpadding: 12px 15px;\n\t\tborder: 1px solid #d9d9d9;\n\t\tborder-radius: 8px;\n\t\tbackground: white;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.picker-arrow {\n\t\tcolor: #999;\n\t\tfont-size: 16px;\n\t}\n\n\t/* 预览区域 */\n\t.preview-content {\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 8px;\n\t\tpadding: 15px;\n\t}\n\n\t.preview-text {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t\tline-height: 1.6;\n\t\tword-break: break-all;\n\t}\n\n\t/* 扫描状态 */\n\t.scan-status {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.scan-indicator {\n\t\twidth: 100px;\n\t\theight: 100px;\n\t\tborder-radius: 50px;\n\t\tbackground: #f0f0f0;\n\t\tmargin: 0 auto 15px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.scan-indicator.scanning {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tanimation: pulse 2s infinite;\n\t}\n\n\t.scan-indicator.found {\n\t\tbackground: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);\n\t}\n\n\t@keyframes pulse {\n\t\t0% { transform: scale(1); }\n\t\t50% { transform: scale(1.1); }\n\t\t100% { transform: scale(1); }\n\t}\n\n\t.scan-icon {\n\t\tfont-size: 35px;\n\t\tcolor: white;\n\t}\n\n\t.scan-text {\n\t\tfont-size: 16px;\n\t\tcolor: #666;\n\t\tfont-weight: bold;\n\t}\n\n\t/* 操作按钮 */\n\t.actions {\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.action-btn {\n\t\twidth: 100%;\n\t\theight: 50px;\n\t\tborder-radius: 12px;\n\t\tmargin-bottom: 15px;\n\t\tborder: none;\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t}\n\n\t.action-btn:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.primary {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tcolor: white;\n\t}\n\n\t.secondary {\n\t\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n\t\tcolor: white;\n\t}\n\n\t.action-btn[disabled] {\n\t\tbackground: #d9d9d9 !important;\n\t\tcolor: #999 !important;\n\t}\n\n\t/* 模板列表 */\n\t.template-list {\n\t\tdisplay: flex;\n\t\tgap: 15px;\n\t}\n\n\t.template-item {\n\t\tflex: 1;\n\t\theight: 70px;\n\t\tborder: 1px solid #f0f0f0;\n\t\tborder-radius: 8px;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: #fafafa;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.template-item:active {\n\t\tbackground: #e6f7ff;\n\t\tborder-color: #1890ff;\n\t}\n\n\t.template-icon {\n\t\tfont-size: 24px;\n\t\tmargin-bottom: 5px;\n\t}\n\n\t.template-label {\n\t\tfont-size: 12px;\n\t\tcolor: #666;\n\t}\n\n\t/* 消息提示 */\n\t.error-message {\n\t\tbackground: #fff2f0;\n\t\tborder: 1px solid #ffccc7;\n\t\tborder-radius: 8px;\n\t\tpadding: 15px;\n\t\tmargin-top: 20px;\n\t}\n\n\t.error-text {\n\t\tfont-size: 14px;\n\t\tcolor: #ff4d4f;\n\t\tline-height: 1.4;\n\t}\n\n\t.success-message {\n\t\tbackground: #f6ffed;\n\t\tborder: 1px solid #b7eb8f;\n\t\tborder-radius: 8px;\n\t\tpadding: 15px;\n\t\tmargin-top: 20px;\n\t}\n\n\t.success-text {\n\t\tfont-size: 14px;\n\t\tcolor: #52c41a;\n\t\tline-height: 1.4;\n\t}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Documents/HBuilderProjects/时代和邻/pages/nfc-write/nfc-write.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["defineComponent", "uni", "nfcManager"], "mappings": ";;;AA8JC,MAAA,YAAeA,8BAAA;AAAA,EACd,OAAI;AACH,WAAO;AAAA,MACN,UAAU;AAAA,MACV,WAAW,IAAA,cAAA;AAAA,QACV,MAAM;AAAA,QACN,KAAK;AAAA,QACL,MAAM,IAAA,cAAA;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,UACV,UAAU;AAAA,SACV;AAAA,QACD,SAAS,IAAA,cAAA;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,SACR;AAAA,OACA;AAAA,MACD,eAAe,CAAC,OAAO,OAAO,MAAM;AAAA,MACpC,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB;AAAA;EAEjB;AAAA,EACD,UAAU;AAAA,IACT,cAAW;AACV,cAAQ,KAAK,UAAQ;AAAA,QACpB,KAAK;AACJ,iBAAO,KAAK,UAAU;AAAA,QACvB,KAAK;AACJ,iBAAO,KAAK,UAAU;AAAA,QACvB,KAAK;AACJ,iBAAO,SAAS,KAAK,UAAU,KAAK,IAAI;AAAA,MAAS,KAAK,UAAU,KAAK,QAAQ;AAAA,MAAS,KAAK,cAAc,KAAK,aAAa,CAAC;AAAA,QAC7H,KAAK;AACJ,iBAAO,OAAO,KAAK,UAAU,QAAQ,IAAI;AAAA,MAAS,KAAK,UAAU,QAAQ,KAAK;AAAA,MAAS,KAAK,UAAU,QAAQ,KAAK;AAAA,QACpH;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA,IACD,WAAQ;AACP,aAAO,KAAK,YAAY,KAAK,YAAY,KAAO,KAAG,KAAK;AAAA,IACzD;AAAA,EACA;AAAA,EACK,SAAM;;AACX,YAAM,KAAK;AAGXC,oBAAAA,MAAI,IAAI,iBAAiB,KAAK,UAAU;AAAA,KACxC;AAAA,EAAA;AAAA,EACD,WAAQ;AAEPA,kBAAAA,MAAI,KAAK,iBAAiB,KAAK,UAAU;AAEzC,QAAI,KAAK,YAAY;AACpBC,gBAAU,WAAC,cAAa;AAAA,IACzB;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACF,iBAAc;;AACnB,YAAI;AACH,gBAAM,SAAS,MAAMA,qBAAW;AAChC,eAAK,eAAe,OAAO;AAC3B,cAAI,CAAC,OAAO,WAAW;AACtB,iBAAK,eAAe,OAAO;AAAA,UAC5B;AAAA,QACC,SAAO,OAAO;AACf,eAAK,eAAe;AACpB,eAAK,eAAe,gBAAgB,MAAM;AAAA,QAC3C;AAAA,OACA;AAAA,IAAA;AAAA,IAED,eAAe,OAAI,MAAA;AAClB,WAAK,WAAW;AAChB,WAAK,cAAa;AAAA,IAClB;AAAA,IAED,iBAAiB,IAAC,MAAA;AACjB,WAAK,gBAAgB,EAAE,OAAO;AAC9B,WAAK,UAAU,KAAK,WAAW,KAAK,cAAc,KAAK,aAAa;AAAA,IACpE;AAAA,IAEK,aAAU;;AACf,YAAI;AACH,cAAI,KAAK,YAAY;AACpB,kBAAMA,UAAAA,WAAW;AACjB,iBAAK,aAAa;AAClBD,0BAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AAAA,UAChD,OAAK;AACN,kBAAMC,UAAAA,WAAW;AACjB,iBAAK,aAAa;AAClB,iBAAK,cAAa;AAClBD,0BAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,UACpD;AAAA,QACC,SAAO,OAAO;AACf,eAAK,eAAe,WAAW,MAAM;AACrCA,wBAAG,MAAC,MAAM,SAAQ,yCAAwC,aAAa,KAAK;AAAA,QAC7E;AAAA,OACA;AAAA,IAAA;AAAA,IAED,WAAW,MAAG,MAAA;AACbA,oBAAG,MAAC,MAAM,OAAM,yCAAwC,gBAAgB,GAAG;AAC3E,WAAK,aAAa;AAClB,WAAK,WAAW;AAChB,WAAK,cAAa;AAElBA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,IACnD;AAAA,IAEK,YAAS;;AACd,YAAI,CAAC,KAAK;AAAU,iBAAM,QAAA,QAAA,IAAA;AAE1B,aAAK,YAAY;AACjB,aAAK,cAAa;AAElB,YAAI;AACH,gBAAM,cAAc,KAAK;AACzB,gBAAMC,UAAAA,WAAW,UAAU,aAAa,KAAK,WAAW,KAAK;AAE7D,eAAK,iBAAiB;AACtBD,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAGhD,qBAAW,MAAA;AACV,iBAAK,iBAAiB;AAAA,UACtB,GAAE,GAAI;AAAA,QAEN,SAAO,OAAO;AACf,eAAK,eAAe,WAAW,MAAM;AACrCA,wBAAG,MAAC,MAAM,SAAQ,yCAAwC,WAAW,KAAK;AAAA,QAC3E,UAAU;AACT,eAAK,YAAY;AAAA,QAClB;AAAA,OACA;AAAA,IAAA;AAAA,IAED,uBAAoB;AACnB,cAAQ,KAAK,UAAQ;AAAA,QACpB,KAAK;AACJ,iBAAO,KAAK,UAAU;AAAA,QACvB,KAAK;AACJ,iBAAO,KAAK,UAAU;AAAA,QACvB,KAAK;AACJ,iBAAO,UAAU,KAAK,UAAU,KAAK,QAAQ,MAAM,KAAK,UAAU,KAAK,IAAI,MAAM,KAAK,UAAU,KAAK,QAAQ;AAAA,QAC9G,KAAK;AACJ,iBAAO;AAAA;AAAA,KAAgC,KAAK,UAAU,QAAQ,IAAI;AAAA,MAAS,KAAK,UAAU,QAAQ,KAAK;AAAA,QAAW,KAAK,UAAU,QAAQ,KAAK;AAAA;AAAA,QAC/I;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA,IAED,YAAY,eAAY,MAAA;AACvB,cAAQ,cAAY;AAAA,QACnB,KAAK;AACJ,eAAK,WAAW;AAChB,eAAK,UAAU,OAAO;AACtB;AAAA,QACD,KAAK;AACJ,eAAK,WAAW;AAChB,eAAK,UAAU,MAAM;AACrB;AAAA,QACD,KAAK;AACJ,eAAK,WAAW;AAChB,eAAK,UAAU,UAAU;AAAA,YACxB,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA;AAER;AAAA,MACF;AACA,WAAK,cAAa;AAAA,IAClB;AAAA,IAED,gBAAa;AACZ,WAAK,eAAe;AACpB,WAAK,iBAAiB;AAAA,IACvB;AAAA,EACD;CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClVD,GAAG,WAAW,eAAe;"}
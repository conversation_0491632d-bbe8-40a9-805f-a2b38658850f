{"version": 3, "file": "nfc-test.js", "sources": ["pages/nfc-test/nfc-test.uvue", "pages/nfc-test/nfc-test.uvue?type=page"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"page-title\">NFC功能测试</text>\n\t\t\t<text class=\"page-subtitle\">测试NFC API是否正常工作</text>\n\t\t</view>\n\n\t\t<!-- 测试结果 -->\n\t\t<view class=\"test-results\">\n\t\t\t<view class=\"test-item\" v-for=\"(test, index) in testResults\" :key=\"index\">\n\t\t\t\t<view class=\"test-header\">\n\t\t\t\t\t<text class=\"test-name\">{{test.name}}</text>\n\t\t\t\t\t<text class=\"test-status\" :class=\"test.status\">\n\t\t\t\t\t\t{{test.status === 'success' ? '✅' : test.status === 'error' ? '❌' : '⏳'}}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"test-message\">{{test.message}}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 操作按钮 -->\n\t\t<view class=\"actions\">\n\t\t\t<button class=\"action-btn primary\" @click=\"runTests\" :disabled=\"isRunning\">\n\t\t\t\t{{isRunning ? '测试中...' : '开始测试'}}\n\t\t\t</button>\n\t\t\t<button class=\"action-btn secondary\" @click=\"clearResults\">\n\t\t\t\t清除结果\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 详细日志 -->\n\t\t<view class=\"logs\" v-if=\"logs.length > 0\">\n\t\t\t<view class=\"section-title\">详细日志</view>\n\t\t\t<view class=\"log-item\" v-for=\"(log, index) in logs\" :key=\"index\">\n\t\t\t\t<text class=\"log-time\">{{formatTime(log.time)}}</text>\n\t\t\t\t<text class=\"log-message\">{{log.message}}</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\timport nfcManager from '@/utils/nfc.js'\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttestResults: [],\n\t\t\t\tlogs: [],\n\t\t\t\tisRunning: false\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.addLog('页面加载完成');\n\t\t},\n\t\tmethods: {\n\t\t\tasync runTests() {\n\t\t\t\tthis.isRunning = true;\n\t\t\t\tthis.testResults = [];\n\t\t\t\tthis.logs = [];\n\t\t\t\tthis.addLog('开始运行NFC功能测试');\n\n\t\t\t\t// 测试1: NFC适配器初始化\n\t\t\t\tawait this.testNFCInit();\n\t\t\t\t\n\t\t\t\t// 测试2: 检查NFC可用性\n\t\t\t\tawait this.testNFCAvailability();\n\t\t\t\t\n\t\t\t\t// 测试3: 测试发现功能\n\t\t\t\tawait this.testDiscovery();\n\t\t\t\t\n\t\t\t\t// 测试4: 测试记录管理\n\t\t\t\tawait this.testRecordManagement();\n\n\t\t\t\tthis.isRunning = false;\n\t\t\t\tthis.addLog('所有测试完成');\n\t\t\t},\n\n\t\t\tasync testNFCInit() {\n\t\t\t\tconst testName = 'NFC适配器初始化';\n\t\t\t\tthis.addLog(`开始测试: ${testName}`);\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 检查nfcManager是否正确初始化\n\t\t\t\t\tif (nfcManager && nfcManager.nfcAdapter) {\n\t\t\t\t\t\tthis.addTestResult(testName, 'success', 'NFC适配器初始化成功');\n\t\t\t\t\t\tthis.addLog('✅ NFC适配器已正确初始化');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.addTestResult(testName, 'error', 'NFC适配器未初始化');\n\t\t\t\t\t\tthis.addLog('❌ NFC适配器初始化失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.addTestResult(testName, 'error', `初始化失败: ${error.message}`);\n\t\t\t\t\tthis.addLog(`❌ 初始化异常: ${error.message}`);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tasync testNFCAvailability() {\n\t\t\t\tconst testName = 'NFC可用性检查';\n\t\t\t\tthis.addLog(`开始测试: ${testName}`);\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst status = await nfcManager.checkNFCAvailability();\n\t\t\t\t\tthis.addTestResult(testName, status.available ? 'success' : 'error', status.message);\n\t\t\t\t\tthis.addLog(`📱 NFC状态: ${status.message}`);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.addTestResult(testName, 'error', `检查失败: ${error.message}`);\n\t\t\t\t\tthis.addLog(`❌ 检查异常: ${error.message}`);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tasync testDiscovery() {\n\t\t\t\tconst testName = 'NFC发现功能';\n\t\t\t\tthis.addLog(`开始测试: ${testName}`);\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 测试开始发现\n\t\t\t\t\tawait nfcManager.startDiscovery();\n\t\t\t\t\tthis.addLog('✅ 开始NFC发现成功');\n\t\t\t\t\t\n\t\t\t\t\t// 等待2秒\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 2000));\n\t\t\t\t\t\n\t\t\t\t\t// 测试停止发现\n\t\t\t\t\tawait nfcManager.stopDiscovery();\n\t\t\t\t\tthis.addLog('✅ 停止NFC发现成功');\n\t\t\t\t\t\n\t\t\t\t\tthis.addTestResult(testName, 'success', 'NFC发现功能正常');\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.addTestResult(testName, 'error', `发现功能失败: ${error.message}`);\n\t\t\t\t\tthis.addLog(`❌ 发现功能异常: ${error.message}`);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tasync testRecordManagement() {\n\t\t\t\tconst testName = '记录管理功能';\n\t\t\t\tthis.addLog(`开始测试: ${testName}`);\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 清除现有记录\n\t\t\t\t\tnfcManager.clearRecords();\n\t\t\t\t\tthis.addLog('🗑️ 清除现有记录');\n\t\t\t\t\t\n\t\t\t\t\t// 模拟添加一条记录\n\t\t\t\t\tnfcManager.records.push({\n\t\t\t\t\t\tid: 'test-' + Date.now(),\n\t\t\t\t\t\ttype: 'read',\n\t\t\t\t\t\ttimestamp: new Date(),\n\t\t\t\t\t\ttagId: 'test-tag',\n\t\t\t\t\t\tdata: '测试数据',\n\t\t\t\t\t\tsuccess: true\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 获取记录\n\t\t\t\t\tconst records = nfcManager.getRecords();\n\t\t\t\t\tif (records.length > 0) {\n\t\t\t\t\t\tthis.addLog(`📋 获取到 ${records.length} 条记录`);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 测试导出\n\t\t\t\t\t\tconst exportData = nfcManager.exportRecords();\n\t\t\t\t\t\tif (exportData && exportData.length > 0) {\n\t\t\t\t\t\t\tthis.addLog('📤 记录导出成功');\n\t\t\t\t\t\t\tthis.addTestResult(testName, 'success', '记录管理功能正常');\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.addTestResult(testName, 'error', '记录导出失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.addTestResult(testName, 'error', '无法获取记录');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.addTestResult(testName, 'error', `记录管理失败: ${error.message}`);\n\t\t\t\t\tthis.addLog(`❌ 记录管理异常: ${error.message}`);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\taddTestResult(name, status, message) {\n\t\t\t\tthis.testResults.push({\n\t\t\t\t\tname,\n\t\t\t\t\tstatus,\n\t\t\t\t\tmessage\n\t\t\t\t});\n\t\t\t},\n\n\t\t\taddLog(message) {\n\t\t\t\tthis.logs.push({\n\t\t\t\t\ttime: new Date(),\n\t\t\t\t\tmessage\n\t\t\t\t});\n\t\t\t\tuni.__f__('log','at pages/nfc-test/nfc-test.uvue:189',`[NFC测试] ${message}`);\n\t\t\t},\n\n\t\t\tclearResults() {\n\t\t\t\tthis.testResults = [];\n\t\t\t\tthis.logs = [];\n\t\t\t\tthis.addLog('清除测试结果');\n\t\t\t},\n\n\t\t\tformatTime(time) {\n\t\t\t\treturn time.toLocaleTimeString();\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tpadding: 20px;\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f8f8f8;\n\t}\n\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.page-title {\n\t\tfont-size: 24px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 8px;\n\t}\n\n\t.page-subtitle {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t.test-results {\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.test-item {\n\t\tbackground: white;\n\t\tborder-radius: 8px;\n\t\tpadding: 15px;\n\t\tmargin-bottom: 10px;\n\t\tbox-shadow: 0 2px 4px rgba(0,0,0,0.1);\n\t}\n\n\t.test-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 5px;\n\t}\n\n\t.test-name {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.test-status {\n\t\tfont-size: 18px;\n\t}\n\n\t.test-message {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t\tline-height: 1.4;\n\t}\n\n\t.actions {\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.action-btn {\n\t\twidth: 100%;\n\t\theight: 50px;\n\t\tborder-radius: 8px;\n\t\tmargin-bottom: 15px;\n\t\tborder: none;\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t}\n\n\t.primary {\n\t\tbackground: #1890ff;\n\t\tcolor: white;\n\t}\n\n\t.secondary {\n\t\tbackground: #f5f5f5;\n\t\tcolor: #333;\n\t}\n\n\t.action-btn[disabled] {\n\t\tbackground: #d9d9d9 !important;\n\t\tcolor: #999 !important;\n\t}\n\n\t.logs {\n\t\tbackground: white;\n\t\tborder-radius: 8px;\n\t\tpadding: 15px;\n\t\tbox-shadow: 0 2px 4px rgba(0,0,0,0.1);\n\t}\n\n\t.section-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 15px;\n\t\tdisplay: block;\n\t}\n\n\t.log-item {\n\t\tdisplay: flex;\n\t\tmargin-bottom: 8px;\n\t\tfont-size: 12px;\n\t}\n\n\t.log-time {\n\t\tcolor: #999;\n\t\tmargin-right: 10px;\n\t\tmin-width: 80px;\n\t}\n\n\t.log-message {\n\t\tcolor: #333;\n\t\tflex: 1;\n\t}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Documents/HBuilderProjects/时代和邻/pages/nfc-test/nfc-test.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["defineComponent", "nfcManager", "uni"], "mappings": ";;;AA4CC,MAAA,YAAeA,8BAAA;AAAA,EACd,OAAI;AACH,WAAO;AAAA,MACN,aAAa,CAAE;AAAA,MACf,MAAM,CAAE;AAAA,MACR,WAAW;AAAA;EAEZ;AAAA,EACD,SAAM;AACL,SAAK,OAAO,QAAQ;AAAA,EACpB;AAAA,EACD,SAAS;AAAA,IACF,WAAQ;;AACb,aAAK,YAAY;AACjB,aAAK,cAAc;AACnB,aAAK,OAAO;AACZ,aAAK,OAAO,aAAa;AAGzB,cAAM,KAAK;AAGX,cAAM,KAAK;AAGX,cAAM,KAAK;AAGX,cAAM,KAAK;AAEX,aAAK,YAAY;AACjB,aAAK,OAAO,QAAQ;AAAA,OACpB;AAAA,IAAA;AAAA,IAEK,cAAW;;AAChB,cAAM,WAAW;AACjB,aAAK,OAAO,SAAS,QAAQ,EAAE;AAE/B,YAAI;AAEH,cAAIC,UAAS,cAAKA,UAAU,WAAC,YAAY;AACxC,iBAAK,cAAc,UAAU,WAAW,aAAa;AACrD,iBAAK,OAAO,gBAAgB;AAAA,UAC3B,OAAK;AACN,iBAAK,cAAc,UAAU,SAAS,YAAY;AAClD,iBAAK,OAAO,eAAe;AAAA,UAC5B;AAAA,QACC,SAAO,OAAO;AACf,eAAK,cAAc,UAAU,SAAS,UAAU,MAAM,OAAO,EAAE;AAC/D,eAAK,OAAO,YAAY,MAAM,OAAO,EAAE;AAAA,QACxC;AAAA,OACA;AAAA,IAAA;AAAA,IAEK,sBAAmB;;AACxB,cAAM,WAAW;AACjB,aAAK,OAAO,SAAS,QAAQ,EAAE;AAE/B,YAAI;AACH,gBAAM,SAAS,MAAMA,qBAAW;AAChC,eAAK,cAAc,UAAU,OAAO,YAAY,YAAY,SAAS,OAAO,OAAO;AACnF,eAAK,OAAO,aAAa,OAAO,OAAO,EAAE;AAAA,QACxC,SAAO,OAAO;AACf,eAAK,cAAc,UAAU,SAAS,SAAS,MAAM,OAAO,EAAE;AAC9D,eAAK,OAAO,WAAW,MAAM,OAAO,EAAE;AAAA,QACvC;AAAA,OACA;AAAA,IAAA;AAAA,IAEK,gBAAa;;AAClB,cAAM,WAAW;AACjB,aAAK,OAAO,SAAS,QAAQ,EAAE;AAE/B,YAAI;AAEH,gBAAMA,UAAAA,WAAW;AACjB,eAAK,OAAO,aAAa;AAGzB,gBAAM,IAAI,QAAQ;AAAW,mBAAA,WAAW,SAAS,GAAI;AAAA,UAAxB,CAAyB;AAGtD,gBAAMA,UAAAA,WAAW;AACjB,eAAK,OAAO,aAAa;AAEzB,eAAK,cAAc,UAAU,WAAW,WAAW;AAAA,QAClD,SAAO,OAAO;AACf,eAAK,cAAc,UAAU,SAAS,WAAW,MAAM,OAAO,EAAE;AAChE,eAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AAAA,QACzC;AAAA,OACA;AAAA,IAAA;AAAA,IAEK,uBAAoB;;AACzB,cAAM,WAAW;AACjB,aAAK,OAAO,SAAS,QAAQ,EAAE;AAE/B,YAAI;AAEHA,oBAAU,WAAC,aAAY;AACvB,eAAK,OAAO,YAAY;AAGxBA,oBAAAA,WAAW,QAAQ,KAAK,IAAA,cAAA;AAAA,YACvB,IAAI,UAAU,KAAK,IAAK;AAAA,YACxB,MAAM;AAAA,YACN,WAAW,oBAAI,KAAM;AAAA,YACrB,OAAO;AAAA,YACP,MAAM;AAAA,YACN,SAAS;AAAA,UACT,CAAA,CAAA;AAGD,gBAAM,UAAUA,qBAAW;AAC3B,cAAI,QAAQ,SAAS,GAAG;AACvB,iBAAK,OAAO,UAAU,QAAQ,MAAM,MAAM;AAG1C,kBAAM,aAAaA,qBAAW;AAC9B,gBAAI,cAAc,WAAW,SAAS,GAAG;AACxC,mBAAK,OAAO,WAAW;AACvB,mBAAK,cAAc,UAAU,WAAW,UAAU;AAAA,YACjD,OAAK;AACN,mBAAK,cAAc,UAAU,SAAS,QAAQ;AAAA,YAC/C;AAAA,UACC,OAAK;AACN,iBAAK,cAAc,UAAU,SAAS,QAAQ;AAAA,UAC/C;AAAA,QACC,SAAO,OAAO;AACf,eAAK,cAAc,UAAU,SAAS,WAAW,MAAM,OAAO,EAAE;AAChE,eAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AAAA,QACzC;AAAA,OACA;AAAA,IAAA;AAAA,IAED,cAAc,OAAI,MAAE,SAAM,MAAE,UAAO,MAAA;AAClC,WAAK,YAAY,KAAK;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACD;AAAA,IAED,OAAO,UAAO,MAAA;AACb,WAAK,KAAK,KAAK;AAAA,QACd,MAAM,oBAAI,KAAM;AAAA,QAChB;AAAA,MACA,CAAA;AACDC,oBAAG,MAAC,MAAM,OAAM,uCAAsC,WAAW,OAAO,EAAE;AAAA,IAC1E;AAAA,IAED,eAAY;AACX,WAAK,cAAc;AACnB,WAAK,OAAO;AACZ,WAAK,OAAO,QAAQ;AAAA,IACpB;AAAA,IAED,WAAW,OAAI,MAAA;AACd,aAAO,KAAK;IACb;AAAA,EACD;CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxMD,GAAG,WAAW,eAAe;"}
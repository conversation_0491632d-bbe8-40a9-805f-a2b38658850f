{"version": 3, "file": "nfc-records.js", "sources": ["pages/nfc-records/nfc-records.uvue", "pages/nfc-records/nfc-records.uvue?type=page"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"page-title\">操作记录</text>\n\t\t\t<text class=\"page-subtitle\">管理NFC读取和写入历史记录</text>\n\t\t</view>\n\n\t\t<!-- 统计信息 -->\n\t\t<view class=\"stats-section\">\n\t\t\t<view class=\"stat-item\">\n\t\t\t\t<text class=\"stat-number\">{{totalRecords}}</text>\n\t\t\t\t<text class=\"stat-label\">总记录</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-item\">\n\t\t\t\t<text class=\"stat-number\">{{readRecords}}</text>\n\t\t\t\t<text class=\"stat-label\">读取</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-item\">\n\t\t\t\t<text class=\"stat-number\">{{writeRecords}}</text>\n\t\t\t\t<text class=\"stat-label\">写入</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-item\">\n\t\t\t\t<text class=\"stat-number\">{{successRate}}%</text>\n\t\t\t\t<text class=\"stat-label\">成功率</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 筛选和操作 -->\n\t\t<view class=\"filter-section\">\n\t\t\t<view class=\"filter-tabs\">\n\t\t\t\t<view class=\"filter-tab\" \n\t\t\t\t\t  :class=\"{ 'active': currentFilter === 'all' }\" \n\t\t\t\t\t  @click=\"setFilter('all')\">\n\t\t\t\t\t全部\n\t\t\t\t</view>\n\t\t\t\t<view class=\"filter-tab\" \n\t\t\t\t\t  :class=\"{ 'active': currentFilter === 'read' }\" \n\t\t\t\t\t  @click=\"setFilter('read')\">\n\t\t\t\t\t读取\n\t\t\t\t</view>\n\t\t\t\t<view class=\"filter-tab\" \n\t\t\t\t\t  :class=\"{ 'active': currentFilter === 'write' }\" \n\t\t\t\t\t  @click=\"setFilter('write')\">\n\t\t\t\t\t写入\n\t\t\t\t</view>\n\t\t\t\t<view class=\"filter-tab\" \n\t\t\t\t\t  :class=\"{ 'active': currentFilter === 'error' }\" \n\t\t\t\t\t  @click=\"setFilter('error')\">\n\t\t\t\t\t错误\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"action-buttons\">\n\t\t\t\t<button class=\"action-btn export\" @click=\"exportRecords\">\n\t\t\t\t\t<text class=\"btn-icon\">📤</text>\n\t\t\t\t\t<text class=\"btn-text\">导出</text>\n\t\t\t\t</button>\n\t\t\t\t<button class=\"action-btn clear\" @click=\"confirmClearRecords\">\n\t\t\t\t\t<text class=\"btn-icon\">🗑️</text>\n\t\t\t\t\t<text class=\"btn-text\">清空</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 记录列表 -->\n\t\t<view class=\"records-list\" v-if=\"filteredRecords.length > 0\">\n\t\t\t<view class=\"record-item\" \n\t\t\t\t  v-for=\"record in filteredRecords\" \n\t\t\t\t  :key=\"record.id\"\n\t\t\t\t  @click=\"viewRecord(record)\"\n\t\t\t\t  @longpress=\"showRecordActions(record)\">\n\t\t\t\t\n\t\t\t\t<view class=\"record-header\">\n\t\t\t\t\t<view class=\"record-type-badge\" :class=\"record.type\">\n\t\t\t\t\t\t<text class=\"badge-text\">{{record.type === 'read' ? '读取' : '写入'}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"record-status\" :class=\"record.success ? 'success' : 'error'\">\n\t\t\t\t\t\t<text class=\"status-icon\">{{record.success ? '✅' : '❌'}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"record-content\">\n\t\t\t\t\t<text class=\"record-preview\">{{getRecordPreview(record)}}</text>\n\t\t\t\t\t<text class=\"record-tag-id\" v-if=\"record.tagId\">标签ID: {{record.tagId}}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"record-footer\">\n\t\t\t\t\t<text class=\"record-time\">{{formatDateTime(record.timestamp)}}</text>\n\t\t\t\t\t<view class=\"record-actions\">\n\t\t\t\t\t\t<text class=\"action-icon\" @click.stop=\"shareRecord(record)\">📤</text>\n\t\t\t\t\t\t<text class=\"action-icon\" @click.stop=\"deleteRecord(record)\">🗑️</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-state\" v-else>\n\t\t\t<text class=\"empty-icon\">📋</text>\n\t\t\t<text class=\"empty-title\">暂无记录</text>\n\t\t\t<text class=\"empty-subtitle\">{{getEmptyMessage()}}</text>\n\t\t\t<button class=\"empty-action\" @click=\"goToRead\">开始使用NFC</button>\n\t\t</view>\n\n\t\t<!-- 加载更多 -->\n\t\t<view class=\"load-more\" v-if=\"hasMore && filteredRecords.length > 0\">\n\t\t\t<button class=\"load-more-btn\" @click=\"loadMore\" :disabled=\"isLoading\">\n\t\t\t\t{{isLoading ? '加载中...' : '加载更多'}}\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\timport nfcManager from '@/utils/nfc.js'\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\trecords: [],\n\t\t\t\tcurrentFilter: 'all',\n\t\t\t\tisLoading: false,\n\t\t\t\thasMore: false,\n\t\t\t\tpageSize: 20,\n\t\t\t\tcurrentPage: 1\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tfilteredRecords() {\n\t\t\t\tlet filtered = this.records;\n\t\t\t\t\n\t\t\t\tswitch (this.currentFilter) {\n\t\t\t\t\tcase 'read':\n\t\t\t\t\t\tfiltered = this.records.filter(r => r.type === 'read');\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'write':\n\t\t\t\t\t\tfiltered = this.records.filter(r => r.type === 'write');\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'error':\n\t\t\t\t\t\tfiltered = this.records.filter(r => !r.success);\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn filtered;\n\t\t\t},\n\t\t\ttotalRecords() {\n\t\t\t\treturn this.records.length;\n\t\t\t},\n\t\t\treadRecords() {\n\t\t\t\treturn this.records.filter(r => r.type === 'read').length;\n\t\t\t},\n\t\t\twriteRecords() {\n\t\t\t\treturn this.records.filter(r => r.type === 'write').length;\n\t\t\t},\n\t\t\tsuccessRate() {\n\t\t\t\tif (this.totalRecords === 0) return 0;\n\t\t\t\tconst successCount = this.records.filter(r => r.success).length;\n\t\t\t\treturn Math.round((successCount / this.totalRecords) * 100);\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.loadRecords();\n\t\t},\n\t\tonShow() {\n\t\t\t// 页面显示时刷新记录\n\t\t\tthis.loadRecords();\n\t\t},\n\t\tmethods: {\n\t\t\tloadRecords() {\n\t\t\t\tthis.records = nfcManager.getRecords();\n\t\t\t\tthis.hasMore = false; // 目前简单实现，不分页\n\t\t\t},\n\t\t\t\n\t\t\tsetFilter(filter) {\n\t\t\t\tthis.currentFilter = filter;\n\t\t\t},\n\t\t\t\n\t\t\tviewRecord(record) {\n\t\t\t\t// 根据记录类型跳转到相应页面\n\t\t\t\tif (record.type === 'read') {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/pages/nfc-read/nfc-read?recordId=${record.id}`\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 显示记录详情\n\t\t\t\t\tthis.showRecordDetail(record);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tshowRecordDetail(record) {\n\t\t\t\tconst content = this.getRecordDetail(record);\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '记录详情',\n\t\t\t\t\tcontent: content,\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '确定'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tshowRecordActions(record) {\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: ['查看详情', '分享记录', '删除记录'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tswitch (res.tapIndex) {\n\t\t\t\t\t\t\tcase 0:\n\t\t\t\t\t\t\t\tthis.showRecordDetail(record);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 1:\n\t\t\t\t\t\t\t\tthis.shareRecord(record);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 2:\n\t\t\t\t\t\t\t\tthis.deleteRecord(record);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tshareRecord(record) {\n\t\t\t\tconst content = this.getRecordDetail(record);\n\t\t\t\tuni.share({\n\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\ttype: 0,\n\t\t\t\t\ttitle: 'NFC操作记录',\n\t\t\t\t\tsummary: content,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({ title: '分享成功', icon: 'success' });\n\t\t\t\t\t},\n\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\tuni.__f__('error','at pages/nfc-records/nfc-records.uvue:230','分享失败:', error);\n\t\t\t\t\t\t// 备用方案：复制到剪贴板\n\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\tdata: content,\n\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\tuni.showToast({ title: '已复制到剪贴板', icon: 'success' });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tdeleteRecord(record) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: '确定要删除这条记录吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tconst success = nfcManager.deleteRecord(record.id);\n\t\t\t\t\t\t\tif (success) {\n\t\t\t\t\t\t\t\tthis.loadRecords();\n\t\t\t\t\t\t\t\tuni.showToast({ title: '删除成功', icon: 'success' });\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({ title: '删除失败', icon: 'error' });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\texportRecords() {\n\t\t\t\ttry {\n\t\t\t\t\tconst data = nfcManager.exportRecords();\n\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\tdata: data,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tuni.showToast({ title: '记录已复制到剪贴板', icon: 'success' });\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({ title: '导出失败', icon: 'error' });\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tconfirmClearRecords() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认清空',\n\t\t\t\t\tcontent: '确定要清空所有记录吗？此操作不可恢复。',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tnfcManager.clearRecords();\n\t\t\t\t\t\t\tthis.loadRecords();\n\t\t\t\t\t\t\tuni.showToast({ title: '记录已清空', icon: 'success' });\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tloadMore() {\n\t\t\t\t// 预留分页加载功能\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}, 1000);\n\t\t\t},\n\t\t\t\n\t\t\tgoToRead() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/nfc-read/nfc-read'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tgetRecordPreview(record) {\n\t\t\t\tif (record.error) {\n\t\t\t\t\treturn `错误: ${record.error}`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (record.type === 'write') {\n\t\t\t\t\treturn `写入: ${record.data ? record.data.substring(0, 30) + '...' : '无数据'}`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (record.messages && record.messages.length > 0) {\n\t\t\t\t\tconst preview = nfcManager.formatNFCData(record.messages[0]);\n\t\t\t\t\treturn preview.length > 50 ? preview.substring(0, 50) + '...' : preview;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn '无数据';\n\t\t\t},\n\t\t\t\n\t\t\tgetRecordDetail(record) {\n\t\t\t\tlet detail = `类型: ${record.type === 'read' ? '读取' : '写入'}\\n`;\n\t\t\t\tdetail += `时间: ${this.formatDateTime(record.timestamp)}\\n`;\n\t\t\t\tdetail += `状态: ${record.success ? '成功' : '失败'}\\n`;\n\t\t\t\t\n\t\t\t\tif (record.tagId) {\n\t\t\t\t\tdetail += `标签ID: ${record.tagId}\\n`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (record.error) {\n\t\t\t\t\tdetail += `错误: ${record.error}\\n`;\n\t\t\t\t} else if (record.data) {\n\t\t\t\t\tdetail += `数据: ${record.data}\\n`;\n\t\t\t\t} else if (record.messages) {\n\t\t\t\t\tdetail += `数据: ${nfcManager.formatNFCData(record.messages)}\\n`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn detail;\n\t\t\t},\n\t\t\t\n\t\t\tgetEmptyMessage() {\n\t\t\t\tswitch (this.currentFilter) {\n\t\t\t\t\tcase 'read':\n\t\t\t\t\t\treturn '还没有读取记录';\n\t\t\t\t\tcase 'write':\n\t\t\t\t\t\treturn '还没有写入记录';\n\t\t\t\t\tcase 'error':\n\t\t\t\t\t\treturn '没有错误记录';\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn '开始使用NFC功能来创建记录';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tformatDateTime(timestamp) {\n\t\t\t\tconst date = new Date(timestamp);\n\t\t\t\treturn `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tpadding: 20px;\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f8f8f8;\n\t}\n\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.page-title {\n\t\tfont-size: 24px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 8px;\n\t}\n\n\t.page-subtitle {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t/* 统计信息 */\n\t.stats-section {\n\t\tdisplay: flex;\n\t\tbackground: white;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t\tmargin-bottom: 20px;\n\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\t}\n\n\t.stat-item {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t}\n\n\t.stat-number {\n\t\tfont-size: 24px;\n\t\tfont-weight: bold;\n\t\tcolor: #1890ff;\n\t\tdisplay: block;\n\t\tmargin-bottom: 5px;\n\t}\n\n\t.stat-label {\n\t\tfont-size: 12px;\n\t\tcolor: #666;\n\t}\n\n\t/* 筛选区域 */\n\t.filter-section {\n\t\tbackground: white;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t\tmargin-bottom: 20px;\n\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\t}\n\n\t.filter-tabs {\n\t\tdisplay: flex;\n\t\tgap: 10px;\n\t\tmargin-bottom: 15px;\n\t}\n\n\t.filter-tab {\n\t\tflex: 1;\n\t\theight: 35px;\n\t\tborder: 1px solid #d9d9d9;\n\t\tborder-radius: 6px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t\tbackground: white;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.filter-tab.active {\n\t\tbackground: #1890ff;\n\t\tcolor: white;\n\t\tborder-color: #1890ff;\n\t}\n\n\t.action-buttons {\n\t\tdisplay: flex;\n\t\tgap: 10px;\n\t}\n\n\t.action-btn {\n\t\tflex: 1;\n\t\theight: 40px;\n\t\tborder-radius: 8px;\n\t\tborder: none;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tgap: 5px;\n\t\tfont-size: 14px;\n\t\tfont-weight: bold;\n\t}\n\n\t.export {\n\t\tbackground: #52c41a;\n\t\tcolor: white;\n\t}\n\n\t.clear {\n\t\tbackground: #ff4d4f;\n\t\tcolor: white;\n\t}\n\n\t.btn-icon {\n\t\tfont-size: 16px;\n\t}\n\n\t.btn-text {\n\t\tfont-size: 14px;\n\t}\n\n\t/* 记录列表 */\n\t.records-list {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.record-item {\n\t\tbackground: white;\n\t\tborder-radius: 12px;\n\t\tpadding: 15px;\n\t\tmargin-bottom: 15px;\n\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.record-item:active {\n\t\ttransform: scale(0.98);\n\t}\n\n\t.record-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10px;\n\t}\n\n\t.record-type-badge {\n\t\tpadding: 4px 8px;\n\t\tborder-radius: 4px;\n\t\tfont-size: 12px;\n\t\tfont-weight: bold;\n\t}\n\n\t.record-type-badge.read {\n\t\tbackground: #e6f7ff;\n\t\tcolor: #1890ff;\n\t}\n\n\t.record-type-badge.write {\n\t\tbackground: #f6ffed;\n\t\tcolor: #52c41a;\n\t}\n\n\t.badge-text {\n\t\tfont-size: 12px;\n\t}\n\n\t.record-status {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.status-icon {\n\t\tfont-size: 16px;\n\t}\n\n\t.record-content {\n\t\tmargin-bottom: 10px;\n\t}\n\n\t.record-preview {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t\tline-height: 1.4;\n\t\tdisplay: block;\n\t\tmargin-bottom: 5px;\n\t}\n\n\t.record-tag-id {\n\t\tfont-size: 12px;\n\t\tcolor: #999;\n\t}\n\n\t.record-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.record-time {\n\t\tfont-size: 12px;\n\t\tcolor: #999;\n\t}\n\n\t.record-actions {\n\t\tdisplay: flex;\n\t\tgap: 10px;\n\t}\n\n\t.action-icon {\n\t\tfont-size: 16px;\n\t\tpadding: 5px;\n\t\tborder-radius: 4px;\n\t\tbackground: #f0f0f0;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.action-icon:active {\n\t\tbackground: #d9d9d9;\n\t}\n\n\t/* 空状态 */\n\t.empty-state {\n\t\ttext-align: center;\n\t\tpadding: 60px 20px;\n\t\tbackground: white;\n\t\tborder-radius: 12px;\n\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\t}\n\n\t.empty-icon {\n\t\tfont-size: 60px;\n\t\tdisplay: block;\n\t\tmargin-bottom: 20px;\n\t\topacity: 0.5;\n\t}\n\n\t.empty-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10px;\n\t}\n\n\t.empty-subtitle {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t\tdisplay: block;\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.empty-action {\n\t\twidth: 150px;\n\t\theight: 40px;\n\t\tbackground: #1890ff;\n\t\tcolor: white;\n\t\tborder: none;\n\t\tborder-radius: 8px;\n\t\tfont-size: 14px;\n\t\tfont-weight: bold;\n\t}\n\n\t/* 加载更多 */\n\t.load-more {\n\t\ttext-align: center;\n\t\tpadding: 20px;\n\t}\n\n\t.load-more-btn {\n\t\twidth: 150px;\n\t\theight: 40px;\n\t\tbackground: white;\n\t\tborder: 1px solid #d9d9d9;\n\t\tborder-radius: 8px;\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t.load-more-btn[disabled] {\n\t\tbackground: #f5f5f5;\n\t\tcolor: #999;\n\t}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Documents/HBuilderProjects/时代和邻/pages/nfc-records/nfc-records.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["defineComponent", "nfcManager", "uni"], "mappings": ";;;AAoHC,MAAA,YAAeA,8BAAA;AAAA,EACd,OAAI;AACH,WAAO;AAAA,MACN,SAAS,CAAE;AAAA,MACX,eAAe;AAAA,MACf,WAAW;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA;EAEd;AAAA,EACD,UAAU;AAAA,IACT,kBAAe;AACd,UAAI,WAAW,KAAK;AAEpB,cAAQ,KAAK,eAAa;AAAA,QACzB,KAAK;AACJ,qBAAW,KAAK,QAAQ,OAAO,OAAA;AAAK,mBAAA,EAAE,SAAS;AAAA,UAAX,CAAiB;AACrD;AAAA,QACD,KAAK;AACJ,qBAAW,KAAK,QAAQ,OAAO,OAAA;AAAK,mBAAA,EAAE,SAAS;AAAA,UAAX,CAAkB;AACtD;AAAA,QACD,KAAK;AACJ,qBAAW,KAAK,QAAQ,OAAO,OAAK;AAAA,mBAAA,CAAC,EAAE;AAAA,UAAH,CAAU;AAC9C;AAAA,MACF;AAEA,aAAO;AAAA,IACP;AAAA,IACD,eAAY;AACX,aAAO,KAAK,QAAQ;AAAA,IACpB;AAAA,IACD,cAAW;AACV,aAAO,KAAK,QAAQ,OAAO,OAAK;AAAA,eAAA,EAAE,SAAS;AAAA,MAAM,CAAA,EAAE;AAAA,IACnD;AAAA,IACD,eAAY;AACX,aAAO,KAAK,QAAQ,OAAO,OAAK;AAAA,eAAA,EAAE,SAAS;AAAA,MAAO,CAAA,EAAE;AAAA,IACpD;AAAA,IACD,cAAW;AACV,UAAI,KAAK,iBAAiB;AAAG,eAAO;AACpC,YAAM,eAAe,KAAK,QAAQ,OAAO;AAAK,eAAA,EAAE;AAAA,MAAO,CAAA,EAAE;AACzD,aAAO,KAAK,MAAO,eAAe,KAAK,eAAgB,GAAG;AAAA,IAC3D;AAAA,EACA;AAAA,EACD,SAAM;AACL,SAAK,YAAW;AAAA,EAChB;AAAA,EACD,SAAM;AAEL,SAAK,YAAW;AAAA,EAChB;AAAA,EACD,SAAS;AAAA,IACR,cAAW;AACV,WAAK,UAAUC,qBAAW;AAC1B,WAAK,UAAU;AAAA,IACf;AAAA,IAED,UAAU,SAAM,MAAA;AACf,WAAK,gBAAgB;AAAA,IACrB;AAAA,IAED,WAAW,SAAM,MAAA;AAEhB,UAAI,OAAO,SAAS,QAAQ;AAC3BC,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,qCAAqC,OAAO,EAAE;AAAA,QACnD,CAAA;AAAA,MACA,OAAK;AAEN,aAAK,iBAAiB,MAAM;AAAA,MAC7B;AAAA,IACA;AAAA,IAED,iBAAiB,SAAM,MAAA;AACtB,YAAM,UAAU,KAAK,gBAAgB,MAAM;AAC3CA,0BAAI,UAAU,IAAA,cAAA;AAAA,QACb,OAAO;AAAA,QACP;AAAA,QACA,YAAY;AAAA,QACZ,aAAa;AAAA,MACb,CAAA,CAAA;AAAA,IACD;AAAA,IAED,kBAAkB,SAAM,MAAA;AACvBA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS,CAAC,QAAG;AACZ,kBAAQ,IAAI,UAAQ;AAAA,YACnB,KAAK;AACJ,mBAAK,iBAAiB,MAAM;AAC5B;AAAA,YACD,KAAK;AACJ,mBAAK,YAAY,MAAM;AACvB;AAAA,YACD,KAAK;AACJ,mBAAK,aAAa,MAAM;AACxB;AAAA,UACF;AAAA,QACD;AAAA,MACA,CAAA;AAAA,IACD;AAAA,IAED,YAAY,SAAM,MAAA;AACjB,YAAM,UAAU,KAAK,gBAAgB,MAAM;AAC3CA,0BAAI,MAAM,IAAA,cAAA;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,MAAA;AACRA,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAAA,QAChD;AAAA,QACD,MAAM,CAAC,UAAK;AACXA,wBAAG,MAAC,MAAM,SAAQ,6CAA4C,SAAS,KAAK;AAE5EA,wBAAAA,MAAI,iBAAiB;AAAA,YACpB,MAAM;AAAA,YACN,SAAS,MAAA;AACRA,4BAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,YACpD;AAAA,UACA,CAAA;AAAA,QACF;AAAA,MACA,CAAA,CAAA;AAAA,IACD;AAAA,IAED,aAAa,SAAM,MAAA;AAClBA,0BAAI,UAAU,IAAA,cAAA;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAG;AACZ,cAAI,IAAI,SAAS;AAChB,kBAAM,UAAUD,UAAU,WAAC,aAAa,OAAO,EAAE;AACjD,gBAAI,SAAS;AACZ,mBAAK,YAAW;AAChBC,4BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAAA,YAC/C,OAAK;AACNA,4BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAM,CAAG;AAAA,YAC/C;AAAA,UACD;AAAA,QACD;AAAA,MACA,CAAA,CAAA;AAAA,IACD;AAAA,IAED,gBAAa;AACZ,UAAI;AACH,cAAM,OAAOD,qBAAW;AACxBC,sBAAAA,MAAI,iBAAiB;AAAA,UACpB;AAAA,UACA,SAAS,MAAA;AACRA,0BAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,UAAU,CAAC;AAAA,UACtD;AAAA,QACA,CAAA;AAAA,MACA,SAAO,OAAO;AACfA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAM,CAAG;AAAA,MAC/C;AAAA,IACA;AAAA,IAED,sBAAmB;AAClBA,0BAAI,UAAU,IAAA,cAAA;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAG;AACZ,cAAI,IAAI,SAAS;AAChBD,sBAAU,WAAC,aAAY;AACvB,iBAAK,YAAW;AAChBC,0BAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AAAA,UAClD;AAAA,QACD;AAAA,MACA,CAAA,CAAA;AAAA,IACD;AAAA,IAED,WAAQ;AAEP,WAAK,YAAY;AACjB,iBAAW,MAAA;AACV,aAAK,YAAY;AAAA,MACjB,GAAE,GAAI;AAAA,IACP;AAAA,IAED,WAAQ;AACPA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACD;AAAA,IAED,iBAAiB,SAAM,MAAA;AACtB,UAAI,OAAO,OAAO;AACjB,eAAO,OAAO,OAAO,KAAK;AAAA,MAC3B;AAEA,UAAI,OAAO,SAAS,SAAS;AAC5B,eAAO,OAAO,OAAO,OAAO,OAAO,KAAK,UAAU,GAAG,EAAE,IAAI,QAAQ,KAAK;AAAA,MACzE;AAEA,UAAI,OAAO,YAAY,OAAO,SAAS,SAAS,GAAG;AAClD,cAAM,UAAUD,UAAAA,WAAW,cAAc,OAAO,SAAS,CAAC,CAAC;AAC3D,eAAO,QAAQ,SAAS,KAAK,QAAQ,UAAU,GAAG,EAAE,IAAI,QAAQ;AAAA,MACjE;AAEA,aAAO;AAAA,IACP;AAAA,IAED,gBAAgB,SAAM,MAAA;AACrB,UAAI,SAAS,OAAO,OAAO,SAAS,SAAS,OAAO,IAAI;AAAA;AACxD,gBAAU,OAAO,KAAK,eAAe,OAAO,SAAS,CAAC;AAAA;AACtD,gBAAU,OAAO,OAAO,UAAU,OAAO,IAAI;AAAA;AAE7C,UAAI,OAAO,OAAO;AACjB,kBAAU,SAAS,OAAO,KAAK;AAAA;AAAA,MAChC;AAEA,UAAI,OAAO,OAAO;AACjB,kBAAU,OAAO,OAAO,KAAK;AAAA;AAAA,MAC9B,WAAW,OAAO,MAAM;AACvB,kBAAU,OAAO,OAAO,IAAI;AAAA;AAAA,MAC3B,WAAS,OAAO,UAAU;AAC3B,kBAAU,OAAOA,qBAAW,cAAc,OAAO,QAAQ,CAAC;AAAA;AAAA,MAC3D;AAEA,aAAO;AAAA,IACP;AAAA,IAED,kBAAe;AACd,cAAQ,KAAK,eAAa;AAAA,QACzB,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA,IAED,eAAe,YAAS,MAAA;AACvB,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,YAAa,CAAA,KAAK,KAAK,aAAa,GAAG,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,QAAO,EAAG,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,SAAU,EAAC,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,WAAU,EAAG,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC;AAAA,IAChO;AAAA,EACD;CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnWD,GAAG,WAAW,eAAe;"}
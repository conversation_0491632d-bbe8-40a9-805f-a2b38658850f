{"version": 3, "file": "nfc-read.js", "sources": ["pages/nfc-read/nfc-read.uvue", "pages/nfc-read/nfc-read.uvue?type=page"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"page-title\">NFC读取</text>\n\t\t\t<text class=\"page-subtitle\">将设备靠近NFC标签进行读取</text>\n\t\t</view>\n\n\t\t<!-- 扫描状态 -->\n\t\t<view class=\"scan-status\">\n\t\t\t<view class=\"scan-indicator\" :class=\"{ 'scanning': isScanning, 'found': tagFound }\">\n\t\t\t\t<text class=\"scan-icon\">{{tagFound ? '✅' : (isScanning ? '📡' : '📱')}}</text>\n\t\t\t</view>\n\t\t\t<text class=\"scan-text\">\n\t\t\t\t{{tagFound ? '已发现标签' : (isScanning ? '正在扫描...' : '点击开始扫描')}}\n\t\t\t</text>\n\t\t</view>\n\n\t\t<!-- 操作按钮 -->\n\t\t<view class=\"actions\">\n\t\t\t<button class=\"action-btn primary\" @click=\"toggleScan\" :disabled=\"!nfcAvailable\">\n\t\t\t\t{{isScanning ? '停止扫描' : '开始扫描'}}\n\t\t\t</button>\n\t\t\t\n\t\t\t<button class=\"action-btn secondary\" @click=\"readNDEF\" \n\t\t\t\t\t:disabled=\"!tagFound || isReading\" v-if=\"tagFound\">\n\t\t\t\t{{isReading ? '读取中...' : '读取NDEF数据'}}\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 标签信息 -->\n\t\t<view class=\"tag-info\" v-if=\"currentTag\">\n\t\t\t<view class=\"info-section\">\n\t\t\t\t<text class=\"section-title\">标签信息</text>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">标签ID:</text>\n\t\t\t\t\t<text class=\"info-value\">{{currentTag.tagId}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">技术类型:</text>\n\t\t\t\t\t<text class=\"info-value\">{{currentTag.techs ? currentTag.techs.join(', ') : '未知'}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">发现时间:</text>\n\t\t\t\t\t<text class=\"info-value\">{{formatDateTime(currentTag.timestamp)}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- NDEF数据显示 -->\n\t\t<view class=\"ndef-data\" v-if=\"ndefData\">\n\t\t\t<view class=\"info-section\">\n\t\t\t\t<text class=\"section-title\">NDEF数据</text>\n\t\t\t\t<view class=\"data-content\">\n\t\t\t\t\t<text class=\"data-text\">{{formattedNdefData}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"data-actions\">\n\t\t\t\t\t<button class=\"mini-btn\" @click=\"copyData\">复制数据</button>\n\t\t\t\t\t<button class=\"mini-btn\" @click=\"shareData\">分享数据</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 原始数据显示 -->\n\t\t<view class=\"raw-data\" v-if=\"currentTag && showRawData\">\n\t\t\t<view class=\"info-section\">\n\t\t\t\t<text class=\"section-title\">原始数据</text>\n\t\t\t\t<view class=\"data-content\">\n\t\t\t\t\t<text class=\"data-text raw\">{{JSON.stringify(currentTag.rawData, null, 2)}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部操作 -->\n\t\t<view class=\"bottom-actions\">\n\t\t\t<button class=\"bottom-btn\" @click=\"toggleRawData\" v-if=\"currentTag\">\n\t\t\t\t{{showRawData ? '隐藏' : '显示'}}原始数据\n\t\t\t</button>\n\t\t\t\n\t\t\t<button class=\"bottom-btn\" @click=\"saveRecord\" v-if=\"currentTag\">\n\t\t\t\t保存记录\n\t\t\t</button>\n\t\t\t\n\t\t\t<button class=\"bottom-btn\" @click=\"clearData\">\n\t\t\t\t清除数据\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 错误提示 -->\n\t\t<view class=\"error-message\" v-if=\"errorMessage\">\n\t\t\t<text class=\"error-text\">{{errorMessage}}</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\timport nfcManager from '@/utils/nfc.js'\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisScanning: false,\n\t\t\t\ttagFound: false,\n\t\t\t\tnfcAvailable: false,\n\t\t\t\tcurrentTag: null,\n\t\t\t\tndefData: null,\n\t\t\t\tisReading: false,\n\t\t\t\tshowRawData: false,\n\t\t\t\terrorMessage: '',\n\t\t\t\trecordId: null // 用于显示特定记录\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tformattedNdefData() {\n\t\t\t\tif (!this.ndefData) return '';\n\t\t\t\treturn nfcManager.formatNFCData(this.ndefData);\n\t\t\t}\n\t\t},\n\t\tasync onLoad(options) {\n\t\t\t// 检查是否是查看特定记录\n\t\t\tif (options.recordId) {\n\t\t\t\tthis.recordId = options.recordId;\n\t\t\t\tthis.loadRecord(options.recordId);\n\t\t\t}\n\t\t\t\n\t\t\tawait this.checkNFCStatus();\n\t\t\t\n\t\t\t// 监听NFC标签发现\n\t\t\tuni.$on('nfc-tag-found', this.onTagFound);\n\t\t},\n\t\tonUnload() {\n\t\t\t// 清理事件监听\n\t\t\tuni.$off('nfc-tag-found', this.onTagFound);\n\t\t\t// 停止扫描\n\t\t\tif (this.isScanning) {\n\t\t\t\tnfcManager.stopDiscovery();\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tasync checkNFCStatus() {\n\t\t\t\ttry {\n\t\t\t\t\tconst status = await nfcManager.checkNFCAvailability();\n\t\t\t\t\tthis.nfcAvailable = status.available;\n\t\t\t\t\tif (!status.available) {\n\t\t\t\t\t\tthis.errorMessage = status.message;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.nfcAvailable = false;\n\t\t\t\t\tthis.errorMessage = '检查NFC状态失败: ' + error.message;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tasync toggleScan() {\n\t\t\t\ttry {\n\t\t\t\t\tif (this.isScanning) {\n\t\t\t\t\t\tawait nfcManager.stopDiscovery();\n\t\t\t\t\t\tthis.isScanning = false;\n\t\t\t\t\t\tuni.showToast({ title: '已停止扫描', icon: 'success' });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait nfcManager.startDiscovery();\n\t\t\t\t\t\tthis.isScanning = true;\n\t\t\t\t\t\tthis.errorMessage = '';\n\t\t\t\t\t\tuni.showToast({ title: '开始扫描NFC', icon: 'success' });\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.errorMessage = '操作失败: ' + error.message;\n\t\t\t\t\tuni.__f__('error','at pages/nfc-read/nfc-read.uvue:166','切换扫描状态失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tonTagFound(tag) {\n\t\t\t\tuni.__f__('log','at pages/nfc-read/nfc-read.uvue:171','读取页面收到NFC标签:', tag);\n\t\t\t\tthis.currentTag = tag;\n\t\t\t\tthis.tagFound = true;\n\t\t\t\tthis.errorMessage = '';\n\t\t\t\t\n\t\t\t\t// 自动尝试读取NDEF数据\n\t\t\t\tthis.readNDEF();\n\t\t\t},\n\t\t\t\n\t\t\tasync readNDEF() {\n\t\t\t\tif (!this.currentTag) return;\n\t\t\t\t\n\t\t\t\tthis.isReading = true;\n\t\t\t\tthis.errorMessage = '';\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst result = await nfcManager.readNDEF(this.currentTag.tagId);\n\t\t\t\t\tthis.ndefData = result;\n\t\t\t\t\tuni.showToast({ title: '读取成功', icon: 'success' });\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.errorMessage = '读取NDEF失败: ' + error.message;\n\t\t\t\t\tuni.__f__('error','at pages/nfc-read/nfc-read.uvue:192','读取NDEF失败:', error);\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isReading = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tloadRecord(recordId) {\n\t\t\t\tconst records = nfcManager.getRecords();\n\t\t\t\tconst record = records.find(r => r.id === recordId);\n\t\t\t\tif (record) {\n\t\t\t\t\tthis.currentTag = record;\n\t\t\t\t\tthis.tagFound = true;\n\t\t\t\t\tif (record.messages) {\n\t\t\t\t\t\tthis.ndefData = { messages: record.messages };\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tcopyData() {\n\t\t\t\tif (!this.formattedNdefData) return;\n\t\t\t\t\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: this.formattedNdefData,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({ title: '已复制到剪贴板', icon: 'success' });\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tshareData() {\n\t\t\t\tif (!this.formattedNdefData) return;\n\t\t\t\t\n\t\t\t\tuni.share({\n\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\ttype: 0,\n\t\t\t\t\ttitle: 'NFC数据分享',\n\t\t\t\t\tsummary: this.formattedNdefData,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({ title: '分享成功', icon: 'success' });\n\t\t\t\t\t},\n\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\tuni.__f__('error','at pages/nfc-read/nfc-read.uvue:233','分享失败:', error);\n\t\t\t\t\t\tuni.showToast({ title: '分享失败', icon: 'error' });\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\ttoggleRawData() {\n\t\t\t\tthis.showRawData = !this.showRawData;\n\t\t\t},\n\t\t\t\n\t\t\tsaveRecord() {\n\t\t\t\tif (!this.currentTag) return;\n\t\t\t\t\n\t\t\t\tuni.showToast({ title: '记录已保存', icon: 'success' });\n\t\t\t},\n\t\t\t\n\t\t\tclearData() {\n\t\t\t\tthis.currentTag = null;\n\t\t\t\tthis.ndefData = null;\n\t\t\t\tthis.tagFound = false;\n\t\t\t\tthis.showRawData = false;\n\t\t\t\tthis.errorMessage = '';\n\t\t\t\tuni.showToast({ title: '数据已清除', icon: 'success' });\n\t\t\t},\n\t\t\t\n\t\t\tformatDateTime(timestamp) {\n\t\t\t\tconst date = new Date(timestamp);\n\t\t\t\treturn `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tpadding: 20px;\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f8f8f8;\n\t}\n\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.page-title {\n\t\tfont-size: 24px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 8px;\n\t}\n\n\t.page-subtitle {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t/* 扫描状态 */\n\t.scan-status {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.scan-indicator {\n\t\twidth: 120px;\n\t\theight: 120px;\n\t\tborder-radius: 60px;\n\t\tbackground: #f0f0f0;\n\t\tmargin: 0 auto 15px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.scan-indicator.scanning {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tanimation: pulse 2s infinite;\n\t}\n\n\t.scan-indicator.found {\n\t\tbackground: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);\n\t}\n\n\t@keyframes pulse {\n\t\t0% { transform: scale(1); }\n\t\t50% { transform: scale(1.1); }\n\t\t100% { transform: scale(1); }\n\t}\n\n\t.scan-icon {\n\t\tfont-size: 40px;\n\t\tcolor: white;\n\t}\n\n\t.scan-text {\n\t\tfont-size: 16px;\n\t\tcolor: #666;\n\t\tfont-weight: bold;\n\t}\n\n\t/* 操作按钮 */\n\t.actions {\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.action-btn {\n\t\twidth: 100%;\n\t\theight: 50px;\n\t\tborder-radius: 12px;\n\t\tmargin-bottom: 15px;\n\t\tborder: none;\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t}\n\n\t.action-btn:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.primary {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tcolor: white;\n\t}\n\n\t.secondary {\n\t\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n\t\tcolor: white;\n\t}\n\n\t.action-btn[disabled] {\n\t\tbackground: #d9d9d9 !important;\n\t\tcolor: #999 !important;\n\t}\n\n\t/* 信息区域 */\n\t.tag-info, .ndef-data, .raw-data {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.info-section {\n\t\tbackground: white;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px;\n\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\t}\n\n\t.section-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 15px;\n\t\tdisplay: block;\n\t}\n\n\t.info-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: flex-start;\n\t\tmargin-bottom: 12px;\n\t\tpadding-bottom: 12px;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t}\n\n\t.info-item:last-child {\n\t\tmargin-bottom: 0;\n\t\tpadding-bottom: 0;\n\t\tborder-bottom: none;\n\t}\n\n\t.info-label {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t\tflex-shrink: 0;\n\t\tmargin-right: 15px;\n\t}\n\n\t.info-value {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t\ttext-align: right;\n\t\tword-break: break-all;\n\t}\n\n\t/* 数据内容 */\n\t.data-content {\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 8px;\n\t\tpadding: 15px;\n\t\tmargin-bottom: 15px;\n\t}\n\n\t.data-text {\n\t\tfont-size: 14px;\n\t\tcolor: #333;\n\t\tline-height: 1.6;\n\t\tword-break: break-all;\n\t}\n\n\t.data-text.raw {\n\t\tfont-family: monospace;\n\t\tfont-size: 12px;\n\t\tcolor: #666;\n\t}\n\n\t.data-actions {\n\t\tdisplay: flex;\n\t\tgap: 10px;\n\t}\n\n\t.mini-btn {\n\t\tflex: 1;\n\t\theight: 35px;\n\t\tborder-radius: 6px;\n\t\tborder: 1px solid #d9d9d9;\n\t\tbackground: white;\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t/* 底部操作 */\n\t.bottom-actions {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 10px;\n\t\tmargin-top: 30px;\n\t}\n\n\t.bottom-btn {\n\t\tflex: 1;\n\t\tmin-width: 100px;\n\t\theight: 40px;\n\t\tborder-radius: 8px;\n\t\tborder: 1px solid #d9d9d9;\n\t\tbackground: white;\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t/* 错误提示 */\n\t.error-message {\n\t\tbackground: #fff2f0;\n\t\tborder: 1px solid #ffccc7;\n\t\tborder-radius: 8px;\n\t\tpadding: 15px;\n\t\tmargin-top: 20px;\n\t}\n\n\t.error-text {\n\t\tfont-size: 14px;\n\t\tcolor: #ff4d4f;\n\t\tline-height: 1.4;\n\t}\n</style>\n", "import MiniProgramPage from '/Users/<USER>/Documents/HBuilderProjects/时代和邻/pages/nfc-read/nfc-read.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["defineComponent", "nfcManager", "uni"], "mappings": ";;;AAiGC,MAAA,YAAeA,8BAAA;AAAA,EACd,OAAI;AACH,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA,MACd,UAAU;AAAA;AAAA;EAEX;AAAA,EACD,UAAU;AAAA,IACT,oBAAiB;AAChB,UAAI,CAAC,KAAK;AAAU,eAAO;AAC3B,aAAOC,qBAAW,cAAc,KAAK,QAAQ;AAAA,IAC9C;AAAA,EACA;AAAA,EACK,OAAO,SAAO;;AAEnB,UAAI,QAAQ,UAAU;AACrB,aAAK,WAAW,QAAQ;AACxB,aAAK,WAAW,QAAQ,QAAQ;AAAA,MACjC;AAEA,YAAM,KAAK;AAGXC,oBAAAA,MAAI,IAAI,iBAAiB,KAAK,UAAU;AAAA,KACxC;AAAA,EAAA;AAAA,EACD,WAAQ;AAEPA,kBAAAA,MAAI,KAAK,iBAAiB,KAAK,UAAU;AAEzC,QAAI,KAAK,YAAY;AACpBD,gBAAU,WAAC,cAAa;AAAA,IACzB;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACF,iBAAc;;AACnB,YAAI;AACH,gBAAM,SAAS,MAAMA,qBAAW;AAChC,eAAK,eAAe,OAAO;AAC3B,cAAI,CAAC,OAAO,WAAW;AACtB,iBAAK,eAAe,OAAO;AAAA,UAC5B;AAAA,QACC,SAAO,OAAO;AACf,eAAK,eAAe;AACpB,eAAK,eAAe,gBAAgB,MAAM;AAAA,QAC3C;AAAA,OACA;AAAA,IAAA;AAAA,IAEK,aAAU;;AACf,YAAI;AACH,cAAI,KAAK,YAAY;AACpB,kBAAMA,UAAAA,WAAW;AACjB,iBAAK,aAAa;AAClBC,0BAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AAAA,UAChD,OAAK;AACN,kBAAMD,UAAAA,WAAW;AACjB,iBAAK,aAAa;AAClB,iBAAK,eAAe;AACpBC,0BAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,UACpD;AAAA,QACC,SAAO,OAAO;AACf,eAAK,eAAe,WAAW,MAAM;AACrCA,wBAAG,MAAC,MAAM,SAAQ,uCAAsC,aAAa,KAAK;AAAA,QAC3E;AAAA,OACA;AAAA,IAAA;AAAA,IAED,WAAW,MAAG,MAAA;AACbA,oBAAG,MAAC,MAAM,OAAM,uCAAsC,gBAAgB,GAAG;AACzE,WAAK,aAAa;AAClB,WAAK,WAAW;AAChB,WAAK,eAAe;AAGpB,WAAK,SAAQ;AAAA,IACb;AAAA,IAEK,WAAQ;;AACb,YAAI,CAAC,KAAK;AAAY,iBAAM,QAAA,QAAA,IAAA;AAE5B,aAAK,YAAY;AACjB,aAAK,eAAe;AAEpB,YAAI;AACH,gBAAM,SAAS,MAAMD,qBAAW,SAAS,KAAK,WAAW,KAAK;AAC9D,eAAK,WAAW;AAChBC,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAAA,QAC/C,SAAO,OAAO;AACf,eAAK,eAAe,eAAe,MAAM;AACzCA,wBAAG,MAAC,MAAM,SAAQ,uCAAsC,aAAa,KAAK;AAAA,QAC3E,UAAU;AACT,eAAK,YAAY;AAAA,QAClB;AAAA,OACA;AAAA,IAAA;AAAA,IAED,WAAW,WAAQ,MAAA;AAClB,YAAM,UAAUD,qBAAW;AAC3B,YAAM,SAAS,QAAQ,KAAK,CAAA,IAAE;AAAG,eAAA,EAAE,OAAO;AAAA,MAAQ,CAAA;AAClD,UAAI,QAAQ;AACX,aAAK,aAAa;AAClB,aAAK,WAAW;AAChB,YAAI,OAAO,UAAU;AACpB,eAAK,WAAW,EAAE,UAAU,OAAO;QACpC;AAAA,MACD;AAAA,IACA;AAAA,IAED,WAAQ;AACP,UAAI,CAAC,KAAK;AAAmB,eAAM;AAEnCC,oBAAAA,MAAI,iBAAiB;AAAA,QACpB,MAAM,KAAK;AAAA,QACX,SAAS,MAAA;AACRA,wBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,QACpD;AAAA,MACA,CAAA;AAAA,IACD;AAAA,IAED,YAAS;AACR,UAAI,CAAC,KAAK;AAAmB,eAAM;AAEnCA,0BAAI,MAAM,IAAA,cAAA;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS,KAAK;AAAA,QACd,SAAS,MAAA;AACRA,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAAA,QAChD;AAAA,QACD,MAAM,CAAC,UAAK;AACXA,wBAAG,MAAC,MAAM,SAAQ,uCAAsC,SAAS,KAAK;AACtEA,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAM,CAAG;AAAA,QAC/C;AAAA,MACA,CAAA,CAAA;AAAA,IACD;AAAA,IAED,gBAAa;AACZ,WAAK,cAAc,CAAC,KAAK;AAAA,IACzB;AAAA,IAED,aAAU;AACT,UAAI,CAAC,KAAK;AAAY,eAAM;AAE5BA,oBAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AAAA,IACjD;AAAA,IAED,YAAS;AACR,WAAK,aAAa;AAClB,WAAK,WAAW;AAChB,WAAK,WAAW;AAChB,WAAK,cAAc;AACnB,WAAK,eAAe;AACpBA,oBAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AAAA,IACjD;AAAA,IAED,eAAe,YAAS,MAAA;AACvB,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,YAAW,CAAE,KAAK,KAAK,SAAW,IAAE,GAAG,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,QAAS,EAAC,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,SAAQ,EAAG,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,WAAY,EAAC,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAAA,IACjR;AAAA,EACD;CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrQD,GAAG,WAAW,eAAe;"}
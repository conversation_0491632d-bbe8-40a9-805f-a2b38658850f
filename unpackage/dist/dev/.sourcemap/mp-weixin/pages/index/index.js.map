{"version": 3, "file": "index.js", "sources": ["pages/index/index.uvue", "pages/index/index.uvue?type=page"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<image class=\"logo\" src=\"/static/logo.png\"></image>\r\n\t\t<view class=\"text-area\">\r\n\t\t\t<text class=\"title\">{{title}}</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- NFC状态显示 -->\r\n\t\t<view class=\"nfc-status\">\r\n\t\t\t<view class=\"status-item\">\r\n\t\t\t\t<text class=\"status-label\">NFC状态:</text>\r\n\t\t\t\t<text class=\"status-value\" :class=\"nfcStatus.available ? 'status-success' : 'status-error'\">\r\n\t\t\t\t\t{{nfcStatus.message}}\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"status-item\" v-if=\"nfcStatus.available\">\r\n\t\t\t\t<text class=\"status-label\">发现状态:</text>\r\n\t\t\t\t<text class=\"status-value\" :class=\"isDiscovering ? 'status-success' : 'status-normal'\">\r\n\t\t\t\t\t{{isDiscovering ? '正在扫描' : '未扫描'}}\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- NFC功能按钮 -->\r\n\t\t<view class=\"nfc-actions\">\r\n\t\t\t<button class=\"action-btn primary\" @click=\"goToRead\" :disabled=\"!nfcStatus.available\">\r\n\t\t\t\t<text class=\"btn-icon\">📖</text>\r\n\t\t\t\t<text class=\"btn-text\">读取NFC</text>\r\n\t\t\t</button>\r\n\r\n\t\t\t<button class=\"action-btn secondary\" @click=\"goToWrite\" :disabled=\"!nfcStatus.available\">\r\n\t\t\t\t<text class=\"btn-icon\">✏️</text>\r\n\t\t\t\t<text class=\"btn-text\">写入NFC</text>\r\n\t\t\t</button>\r\n\r\n\t\t\t<button class=\"action-btn tertiary\" @click=\"goToRecords\">\r\n\t\t\t\t<text class=\"btn-icon\">📋</text>\r\n\t\t\t\t<text class=\"btn-text\">操作记录</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 快速操作 -->\r\n\t\t<view class=\"quick-actions\" v-if=\"nfcStatus.available\">\r\n\t\t\t<button class=\"quick-btn\" @click=\"toggleDiscovery\"\r\n\t\t\t\t\t:class=\"isDiscovering ? 'stop-btn' : 'start-btn'\">\r\n\t\t\t\t{{isDiscovering ? '停止扫描' : '开始扫描'}}\r\n\t\t\t</button>\r\n\r\n\t\t\t<button class=\"quick-btn refresh-btn\" @click=\"refreshNFCStatus\">\r\n\t\t\t\t刷新状态\r\n\t\t\t</button>\r\n\r\n\t\t\t<button class=\"quick-btn test-btn\" @click=\"goToTest\">\r\n\t\t\t\t功能测试\r\n\t\t\t</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 最近记录 -->\r\n\t\t<view class=\"recent-records\" v-if=\"recentRecords.length > 0\">\r\n\t\t\t<view class=\"section-title\">最近操作</view>\r\n\t\t\t<view class=\"record-item\" v-for=\"record in recentRecords\" :key=\"record.id\" @click=\"viewRecord(record)\">\r\n\t\t\t\t<view class=\"record-header\">\r\n\t\t\t\t\t<text class=\"record-type\" :class=\"record.type\">{{record.type === 'read' ? '读取' : '写入'}}</text>\r\n\t\t\t\t\t<text class=\"record-time\">{{formatTime(record.timestamp)}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"record-content\">{{getRecordPreview(record)}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script lang=\"uts\">\r\n\timport nfcManager from '@/utils/nfc.js'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: 'NFC管理工具',\r\n\t\t\t\tnfcStatus: {\r\n\t\t\t\t\tavailable: false,\r\n\t\t\t\t\tmessage: '检查中...'\r\n\t\t\t\t},\r\n\t\t\t\tisDiscovering: false,\r\n\t\t\t\trecentRecords: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync onLoad() {\r\n\t\t\tawait this.initNFC();\r\n\t\t\tthis.loadRecentRecords();\r\n\r\n\t\t\t// 监听NFC标签发现事件\r\n\t\t\tuni.$on('nfc-tag-found', this.onTagFound);\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\t// 清理事件监听\r\n\t\t\tuni.$off('nfc-tag-found', this.onTagFound);\r\n\t\t\t// 停止NFC发现\r\n\t\t\tif (this.isDiscovering) {\r\n\t\t\t\tnfcManager.stopDiscovery();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync initNFC() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst status = await nfcManager.checkNFCAvailability();\r\n\t\t\t\t\tthis.nfcStatus = status;\r\n\t\t\t\t\tthis.isDiscovering = status.discovering || false;\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.__f__('error','at pages/index/index.uvue:109','初始化NFC失败:', error);\r\n\t\t\t\t\tthis.nfcStatus = {\r\n\t\t\t\t\t\tavailable: false,\r\n\t\t\t\t\t\tmessage: '初始化失败: ' + error.message\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tasync refreshNFCStatus() {\r\n\t\t\t\tuni.showLoading({ title: '检查中...' });\r\n\t\t\t\tawait this.initNFC();\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: this.nfcStatus.available ? 'NFC可用' : 'NFC不可用',\r\n\t\t\t\t\ticon: this.nfcStatus.available ? 'success' : 'error'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tasync toggleDiscovery() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tif (this.isDiscovering) {\r\n\t\t\t\t\t\tawait nfcManager.stopDiscovery();\r\n\t\t\t\t\t\tthis.isDiscovering = false;\r\n\t\t\t\t\t\tuni.showToast({ title: '已停止扫描', icon: 'success' });\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tawait nfcManager.startDiscovery();\r\n\t\t\t\t\t\tthis.isDiscovering = true;\r\n\t\t\t\t\t\tuni.showToast({ title: '开始扫描NFC', icon: 'success' });\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.__f__('error','at pages/index/index.uvue:139','切换发现状态失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '操作失败: ' + error.message,\r\n\t\t\t\t\t\ticon: 'error'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tgoToRead() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/nfc-read/nfc-read'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgoToWrite() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/nfc-write/nfc-write'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgoToRecords() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/nfc-records/nfc-records'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgoToTest() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/nfc-test/nfc-test'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tloadRecentRecords() {\r\n\t\t\t\tthis.recentRecords = nfcManager.getRecords(3);\r\n\t\t\t},\r\n\r\n\t\t\tonTagFound(record) {\r\n\t\t\t\tuni.__f__('log','at pages/index/index.uvue:176','主页面收到NFC标签:', record);\r\n\t\t\t\tthis.loadRecentRecords();\r\n\r\n\t\t\t\t// 显示发现提示\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '发现NFC标签',\r\n\t\t\t\t\tcontent: `标签ID: ${record.tagId}\\n是否查看详情？`,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tthis.viewRecord(record);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tviewRecord(record) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/nfc-read/nfc-read?recordId=${record.id}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tformatTime(timestamp) {\r\n\t\t\t\tconst date = new Date(timestamp);\r\n\t\t\t\treturn `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\r\n\t\t\t},\r\n\r\n\t\t\tgetRecordPreview(record) {\r\n\t\t\t\tif (record.error) {\r\n\t\t\t\t\treturn `错误: ${record.error}`;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (record.type === 'write') {\r\n\t\t\t\t\treturn `写入: ${record.data ? record.data.substring(0, 20) + '...' : '无数据'}`;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (record.messages && record.messages.length > 0) {\r\n\t\t\t\t\tconst preview = nfcManager.formatNFCData(record.messages[0]);\r\n\t\t\t\t\treturn preview.length > 30 ? preview.substring(0, 30) + '...' : preview;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn '无数据';\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.container {\r\n\t\tpadding: 20px;\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f8f8f8;\r\n\t}\r\n\r\n\t.logo {\r\n\t\theight: 80px;\r\n\t\twidth: 80px;\r\n\t\tmargin: 20px auto 15px auto;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 24px;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n\r\n\t/* NFC状态显示 */\r\n\t.nfc-status {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 12px;\r\n\t\tpadding: 20px;\r\n\t\tmargin-bottom: 20px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\t}\r\n\r\n\t.status-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.status-item:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.status-label {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.status-value {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.status-success {\r\n\t\tcolor: #52c41a;\r\n\t}\r\n\r\n\t.status-error {\r\n\t\tcolor: #ff4d4f;\r\n\t}\r\n\r\n\t.status-normal {\r\n\t\tcolor: #1890ff;\r\n\t}\r\n\r\n\t/* 功能按钮 */\r\n\t.nfc-actions {\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\t.action-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 60px;\r\n\t\tborder-radius: 12px;\r\n\t\tmargin-bottom: 15px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder: none;\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.action-btn:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.primary {\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.secondary {\r\n\t\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.tertiary {\r\n\t\tbackground: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.action-btn[disabled] {\r\n\t\tbackground: #d9d9d9 !important;\r\n\t\tcolor: #999 !important;\r\n\t}\r\n\r\n\t.btn-icon {\r\n\t\tfont-size: 20px;\r\n\t\tmargin-right: 10px;\r\n\t}\r\n\r\n\t.btn-text {\r\n\t\tfont-size: 16px;\r\n\t}\r\n\r\n\t/* 快速操作 */\r\n\t.quick-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 10px;\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\t.quick-btn {\r\n\t\tflex: 1;\r\n\t\theight: 40px;\r\n\t\tborder-radius: 8px;\r\n\t\tborder: none;\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.start-btn {\r\n\t\tbackground: #52c41a;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.stop-btn {\r\n\t\tbackground: #ff4d4f;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.refresh-btn {\r\n\t\tbackground: #1890ff;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t.test-btn {\r\n\t\tbackground: #52c41a;\r\n\t\tcolor: white;\r\n\t}\r\n\r\n\t/* 最近记录 */\r\n\t.recent-records {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 12px;\r\n\t\tpadding: 20px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.record-item {\r\n\t\tpadding: 15px;\r\n\t\tborder: 1px solid #f0f0f0;\r\n\t\tborder-radius: 8px;\r\n\t\tmargin-bottom: 10px;\r\n\t\tbackground: #fafafa;\r\n\t}\r\n\r\n\t.record-item:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.record-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 8px;\r\n\t}\r\n\r\n\t.record-type {\r\n\t\tfont-size: 12px;\r\n\t\tpadding: 2px 8px;\r\n\t\tborder-radius: 4px;\r\n\t\tcolor: white;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.record-type.read {\r\n\t\tbackground: #52c41a;\r\n\t}\r\n\r\n\t.record-type.write {\r\n\t\tbackground: #1890ff;\r\n\t}\r\n\r\n\t.record-time {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.record-content {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.4;\r\n\t}\r\n</style>\r\n", "import MiniProgramPage from '/Users/<USER>/Documents/HBuilderProjects/时代和邻/pages/index/index.uvue'\nwx.createPage(MiniProgramPage)"], "names": ["defineComponent", "uni", "nfcManager"], "mappings": ";;;;AA0EC,MAAA,YAAeA,8BAAA;AAAA,EACd,OAAI;AACH,WAAO;AAAA,MACN,OAAO;AAAA,MACP,WAAW,IAAA,cAAA;AAAA,QACV,WAAW;AAAA,QACX,SAAS;AAAA,OACT;AAAA,MACD,eAAe;AAAA,MACf,eAAe,CAAC;AAAA;EAEjB;AAAA,EACK,SAAM;;AACX,YAAM,KAAK;AACX,WAAK,kBAAiB;AAGtBC,oBAAAA,MAAI,IAAI,iBAAiB,KAAK,UAAU;AAAA,KACxC;AAAA,EAAA;AAAA,EACD,WAAQ;AAEPA,kBAAAA,MAAI,KAAK,iBAAiB,KAAK,UAAU;AAEzC,QAAI,KAAK,eAAe;AACvBC,gBAAU,WAAC,cAAa;AAAA,IACzB;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACF,UAAO;;AACZ,YAAI;AACH,gBAAM,SAAS,MAAMA,qBAAW;AAChC,eAAK,YAAY;AACjB,eAAK,gBAAgB,OAAO,eAAe;AAAA,QAC1C,SAAO,OAAO;AACfD,wBAAG,MAAC,MAAM,SAAQ,iCAAgC,aAAa,KAAK;AACpE,eAAK,YAAY;AAAA,YAChB,WAAW;AAAA,YACX,SAAS,YAAY,MAAM;AAAA;QAE7B;AAAA,OACA;AAAA,IAAA;AAAA,IAEK,mBAAgB;;AACrBA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,cAAM,KAAK;AACXA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,KAAK,UAAU,YAAY,UAAU;AAAA,UAC5C,MAAM,KAAK,UAAU,YAAY,YAAY;AAAA,QAC7C,CAAA;AAAA,OACD;AAAA,IAAA;AAAA,IAEK,kBAAe;;AACpB,YAAI;AACH,cAAI,KAAK,eAAe;AACvB,kBAAMC,UAAAA,WAAW;AACjB,iBAAK,gBAAgB;AACrBD,0BAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AAAA,UAChD,OAAK;AACN,kBAAMC,UAAAA,WAAW;AACjB,iBAAK,gBAAgB;AACrBD,0BAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,UACpD;AAAA,QACC,SAAO,OAAO;AACfA,wBAAG,MAAC,MAAM,SAAQ,iCAAgC,aAAa,KAAK;AACpEA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,WAAW,MAAM;AAAA,YACxB,MAAM;AAAA,UACN,CAAA;AAAA,QACF;AAAA,OACA;AAAA,IAAA;AAAA,IAED,WAAQ;AACPA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACD;AAAA,IAED,YAAS;AACRA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACD;AAAA,IAED,cAAW;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACD;AAAA,IAED,WAAQ;AACPA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACL,CAAA;AAAA,IACD;AAAA,IAED,oBAAiB;AAChB,WAAK,gBAAgBC,UAAAA,WAAW,WAAW,CAAC;AAAA,IAC5C;AAAA,IAED,WAAW,SAAM,MAAA;AAChBD,oBAAG,MAAC,MAAM,OAAM,iCAAgC,eAAe,MAAM;AACrE,WAAK,kBAAiB;AAGtBA,0BAAI,UAAU,IAAA,cAAA;AAAA,QACb,OAAO;AAAA,QACP,SAAS,SAAS,OAAO,KAAK;AAAA;AAAA,QAC9B,SAAS,CAAC,QAAG;AACZ,cAAI,IAAI,SAAS;AAChB,iBAAK,WAAW,MAAM;AAAA,UACvB;AAAA,QACD;AAAA,MACA,CAAA,CAAA;AAAA,IACD;AAAA,IAED,WAAW,SAAM,MAAA;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,qCAAqC,OAAO,EAAE;AAAA,MACnD,CAAA;AAAA,IACD;AAAA,IAED,WAAW,YAAS,MAAA;AACnB,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC,IAAI,KAAK,WAAY,EAAC,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAAA,IACtG;AAAA,IAED,iBAAiB,SAAM,MAAA;AACtB,UAAI,OAAO,OAAO;AACjB,eAAO,OAAO,OAAO,KAAK;AAAA,MAC3B;AAEA,UAAI,OAAO,SAAS,SAAS;AAC5B,eAAO,OAAO,OAAO,OAAO,OAAO,KAAK,UAAU,GAAG,EAAE,IAAI,QAAQ,KAAK;AAAA,MACzE;AAEA,UAAI,OAAO,YAAY,OAAO,SAAS,SAAS,GAAG;AAClD,cAAM,UAAUC,UAAAA,WAAW,cAAc,OAAO,SAAS,CAAC,CAAC;AAC3D,eAAO,QAAQ,SAAS,KAAK,QAAQ,UAAU,GAAG,EAAE,IAAI,QAAQ;AAAA,MACjE;AAEA,aAAO;AAAA,IACR;AAAA,EACD;CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzND,GAAG,WAAW,eAAe;"}
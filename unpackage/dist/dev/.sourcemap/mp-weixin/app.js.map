{"version": 3, "file": "app.js", "sources": ["App.uvue", "main.uts"], "sourcesContent": ["<script lang=\"uts\">\r\n\tlet firstBackTime = 0\r\n\texport default {\r\n\t\tonLaunch: function () {\r\n\t\t\tuni.__f__('log','at App.uvue:5','App Launch')\r\n\t\t},\r\n\t\tonShow: function () {\r\n\t\t\tuni.__f__('log','at App.uvue:8','App Show')\r\n\t\t},\r\n\t\tonHide: function () {\r\n\t\t\tuni.__f__('log','at App.uvue:11','App Hide')\r\n\t\t},\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\tonExit: function () {\r\n\t\t\tuni.__f__('log','at App.uvue:32','App Exit')\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\t.uni-row {\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.uni-column {\r\n\t\tflex-direction: column;\r\n\t}\r\n</style>", "import App from './App.uvue'\r\n\r\nimport { createSSRApp } from 'vue'\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}"], "names": ["defineComponent", "uni", "createSSRApp", "App"], "mappings": ";;;;;;;;;;AAEC,MAAA,YAAeA,cAAA,gBAAA,IAAA,cAAA;AAAA,EACd,UAAU,WAAA;AACTC,kBAAAA,MAAI,MAAM,OAAM,iBAAgB,YAAY;AAAA,EAC5C;AAAA,EACD,QAAQ,WAAA;AACPA,kBAAAA,MAAI,MAAM,OAAM,iBAAgB,UAAU;AAAA,EAC1C;AAAA,EACD,QAAQ,WAAA;AACPA,kBAAAA,MAAI,MAAM,OAAM,kBAAiB,UAAU;AAAA,EAC3C;AAAA,EAmBD,QAAQ,WAAA;AACPA,kBAAAA,MAAI,MAAM,OAAM,kBAAiB,UAAU;AAAA,EAC3C;CACF,CAAA;SC9Be,YAAS;AACxB,QAAM,MAAMC,2BAAaC,SAAG;AAC5B,SAAO;AAAA,IACN;AAAA;AAEF;AACA,YAAY,IAAI,MAAM,MAAM;;"}
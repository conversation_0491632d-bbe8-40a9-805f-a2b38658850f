<template>
	<view class="container">
		<view class="header">
			<text class="page-title">NFC写入</text>
			<text class="page-subtitle">输入数据并写入到NFC标签</text>
		</view>

		<!-- 数据输入区域 -->
		<view class="input-section">
			<view class="section-title">输入数据</view>
			
			<!-- 数据类型选择 -->
			<view class="data-type-selector">
				<view class="type-item" 
					  :class="{ 'active': dataType === 'text' }" 
					  @click="selectDataType('text')">
					<text class="type-icon">📝</text>
					<text class="type-label">文本</text>
				</view>
				<view class="type-item" 
					  :class="{ 'active': dataType === 'url' }" 
					  @click="selectDataType('url')">
					<text class="type-icon">🔗</text>
					<text class="type-label">网址</text>
				</view>
				<view class="type-item" 
					  :class="{ 'active': dataType === 'wifi' }" 
					  @click="selectDataType('wifi')">
					<text class="type-icon">📶</text>
					<text class="type-label">WiFi</text>
				</view>
				<view class="type-item" 
					  :class="{ 'active': dataType === 'contact' }" 
					  @click="selectDataType('contact')">
					<text class="type-icon">👤</text>
					<text class="type-label">联系人</text>
				</view>
			</view>

			<!-- 文本输入 -->
			<view class="input-area" v-if="dataType === 'text'">
				<textarea class="data-input" 
						  v-model="inputData.text" 
						  placeholder="请输入要写入的文本内容"
						  :maxlength="500">
				</textarea>
				<text class="char-count">{{inputData.text.length}}/500</text>
			</view>

			<!-- URL输入 -->
			<view class="input-area" v-if="dataType === 'url'">
				<input class="data-input" 
					   v-model="inputData.url" 
					   placeholder="请输入网址，如：https://www.example.com"
					   type="url">
			</view>

			<!-- WiFi信息输入 -->
			<view class="input-area" v-if="dataType === 'wifi'">
				<input class="data-input" 
					   v-model="inputData.wifi.ssid" 
					   placeholder="WiFi名称(SSID)"
					   style="margin-bottom: 15px;">
				<input class="data-input" 
					   v-model="inputData.wifi.password" 
					   placeholder="WiFi密码"
					   type="password"
					   style="margin-bottom: 15px;">
				<picker @change="onSecurityChange" :value="securityIndex" :range="securityTypes">
					<view class="picker-input">
						<text>加密类型: {{securityTypes[securityIndex]}}</text>
						<text class="picker-arrow">></text>
					</view>
				</picker>
			</view>

			<!-- 联系人信息输入 -->
			<view class="input-area" v-if="dataType === 'contact'">
				<input class="data-input" 
					   v-model="inputData.contact.name" 
					   placeholder="姓名"
					   style="margin-bottom: 15px;">
				<input class="data-input" 
					   v-model="inputData.contact.phone" 
					   placeholder="电话号码"
					   type="tel"
					   style="margin-bottom: 15px;">
				<input class="data-input" 
					   v-model="inputData.contact.email" 
					   placeholder="邮箱地址"
					   type="email">
			</view>
		</view>

		<!-- 预览区域 -->
		<view class="preview-section" v-if="previewData">
			<view class="section-title">数据预览</view>
			<view class="preview-content">
				<text class="preview-text">{{previewData}}</text>
			</view>
		</view>

		<!-- 扫描状态 -->
		<view class="scan-status">
			<view class="scan-indicator" :class="{ 'scanning': isScanning, 'found': tagFound }">
				<text class="scan-icon">{{tagFound ? '✅' : (isScanning ? '📡' : '📱')}}</text>
			</view>
			<text class="scan-text">
				{{tagFound ? '已发现标签，可以写入' : (isScanning ? '请将标签靠近设备' : '点击开始扫描标签')}}
			</text>
		</view>

		<!-- 操作按钮 -->
		<view class="actions">
			<button class="action-btn primary" @click="toggleScan" :disabled="!nfcAvailable">
				{{isScanning ? '停止扫描' : '扫描标签'}}
			</button>
			
			<button class="action-btn secondary" @click="writeData" 
					:disabled="!canWrite || isWriting">
				{{isWriting ? '写入中...' : '写入数据'}}
			</button>
		</view>

		<!-- 快捷数据模板 -->
		<view class="templates-section">
			<view class="section-title">快捷模板</view>
			<view class="template-list">
				<view class="template-item" @click="useTemplate('hello')">
					<text class="template-icon">👋</text>
					<text class="template-label">问候语</text>
				</view>
				<view class="template-item" @click="useTemplate('website')">
					<text class="template-icon">🌐</text>
					<text class="template-label">官网</text>
				</view>
				<view class="template-item" @click="useTemplate('contact')">
					<text class="template-icon">📞</text>
					<text class="template-label">联系方式</text>
				</view>
			</view>
		</view>

		<!-- 错误提示 -->
		<view class="error-message" v-if="errorMessage">
			<text class="error-text">{{errorMessage}}</text>
		</view>

		<!-- 成功提示 -->
		<view class="success-message" v-if="successMessage">
			<text class="success-text">{{successMessage}}</text>
		</view>
	</view>
</template>

<script>
	import nfcManager from '@/utils/nfc.js'
	
	export default {
		data() {
			return {
				dataType: 'text',
				inputData: {
					text: '',
					url: '',
					wifi: {
						ssid: '',
						password: '',
						security: 'WPA'
					},
					contact: {
						name: '',
						phone: '',
						email: ''
					}
				},
				securityTypes: ['WPA', 'WEP', 'Open'],
				securityIndex: 0,
				isScanning: false,
				tagFound: false,
				currentTag: null,
				nfcAvailable: false,
				isWriting: false,
				errorMessage: '',
				successMessage: ''
			}
		},
		computed: {
			previewData() {
				switch (this.dataType) {
					case 'text':
						return this.inputData.text;
					case 'url':
						return this.inputData.url;
					case 'wifi':
						return `WiFi: ${this.inputData.wifi.ssid}\n密码: ${this.inputData.wifi.password}\n加密: ${this.securityTypes[this.securityIndex]}`;
					case 'contact':
						return `姓名: ${this.inputData.contact.name}\n电话: ${this.inputData.contact.phone}\n邮箱: ${this.inputData.contact.email}`;
					default:
						return '';
				}
			},
			canWrite() {
				return this.tagFound && this.previewData.trim() && this.nfcAvailable;
			}
		},
		async onLoad() {
			await this.checkNFCStatus();
			
			// 监听NFC标签发现
			uni.$on('nfc-tag-found', this.onTagFound);
		},
		onUnload() {
			// 清理事件监听
			uni.$off('nfc-tag-found', this.onTagFound);
			// 停止扫描
			if (this.isScanning) {
				nfcManager.stopDiscovery();
			}
		},
		methods: {
			async checkNFCStatus() {
				try {
					const status = await nfcManager.checkNFCAvailability();
					this.nfcAvailable = status.available;
					if (!status.available) {
						this.errorMessage = status.message;
					}
				} catch (error) {
					this.nfcAvailable = false;
					this.errorMessage = '检查NFC状态失败: ' + error.message;
				}
			},
			
			selectDataType(type) {
				this.dataType = type;
				this.clearMessages();
			},
			
			onSecurityChange(e) {
				this.securityIndex = e.detail.value;
				this.inputData.wifi.security = this.securityTypes[this.securityIndex];
			},
			
			async toggleScan() {
				try {
					if (this.isScanning) {
						await nfcManager.stopDiscovery();
						this.isScanning = false;
						uni.showToast({ title: '已停止扫描', icon: 'success' });
					} else {
						await nfcManager.startDiscovery();
						this.isScanning = true;
						this.clearMessages();
						uni.showToast({ title: '开始扫描NFC', icon: 'success' });
					}
				} catch (error) {
					this.errorMessage = '操作失败: ' + error.message;
					console.error('切换扫描状态失败:', error);
				}
			},
			
			onTagFound(tag) {
				console.log('写入页面收到NFC标签:', tag);
				this.currentTag = tag;
				this.tagFound = true;
				this.clearMessages();
				
				uni.showToast({ title: '发现NFC标签', icon: 'success' });
			},
			
			async writeData() {
				if (!this.canWrite) return;
				
				this.isWriting = true;
				this.clearMessages();
				
				try {
					const dataToWrite = this.formatDataForWriting();
					await nfcManager.writeNDEF(dataToWrite, this.currentTag.tagId);
					
					this.successMessage = '数据写入成功！';
					uni.showToast({ title: '写入成功', icon: 'success' });
					
					// 3秒后清除成功消息
					setTimeout(() => {
						this.successMessage = '';
					}, 3000);
					
				} catch (error) {
					this.errorMessage = '写入失败: ' + error.message;
					console.error('写入数据失败:', error);
				} finally {
					this.isWriting = false;
				}
			},
			
			formatDataForWriting() {
				switch (this.dataType) {
					case 'text':
						return this.inputData.text;
					case 'url':
						return this.inputData.url;
					case 'wifi':
						return `WIFI:T:${this.inputData.wifi.security};S:${this.inputData.wifi.ssid};P:${this.inputData.wifi.password};;`;
					case 'contact':
						return `BEGIN:VCARD\nVERSION:3.0\nFN:${this.inputData.contact.name}\nTEL:${this.inputData.contact.phone}\nEMAIL:${this.inputData.contact.email}\nEND:VCARD`;
					default:
						return '';
				}
			},
			
			useTemplate(templateType) {
				switch (templateType) {
					case 'hello':
						this.dataType = 'text';
						this.inputData.text = '你好！欢迎使用NFC功能！';
						break;
					case 'website':
						this.dataType = 'url';
						this.inputData.url = 'https://www.example.com';
						break;
					case 'contact':
						this.dataType = 'contact';
						this.inputData.contact = {
							name: '张三',
							phone: '13800138000',
							email: '<EMAIL>'
						};
						break;
				}
				this.clearMessages();
			},
			
			clearMessages() {
				this.errorMessage = '';
				this.successMessage = '';
			}
		}
	}
</script>

<style>
	.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
	}

	.header {
		text-align: center;
		margin-bottom: 30px;
	}

	.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8px;
	}

	.page-subtitle {
		font-size: 14px;
		color: #666;
	}

	/* 输入区域 */
	.input-section, .preview-section, .templates-section {
		background: white;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	}

	.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
		display: block;
	}

	/* 数据类型选择器 */
	.data-type-selector {
		display: flex;
		gap: 10px;
		margin-bottom: 20px;
		flex-wrap: wrap;
	}

	.type-item {
		flex: 1;
		min-width: 70px;
		height: 60px;
		border: 2px solid #f0f0f0;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: white;
		transition: all 0.3s ease;
	}

	.type-item.active {
		border-color: #1890ff;
		background: #e6f7ff;
	}

	.type-icon {
		font-size: 20px;
		margin-bottom: 4px;
	}

	.type-label {
		font-size: 12px;
		color: #666;
	}

	.type-item.active .type-label {
		color: #1890ff;
		font-weight: bold;
	}

	/* 输入区域 */
	.input-area {
		margin-bottom: 15px;
	}

	.data-input {
		width: 100%;
		padding: 12px 15px;
		border: 1px solid #d9d9d9;
		border-radius: 8px;
		font-size: 16px;
		background: white;
		box-sizing: border-box;
	}

	.data-input:focus {
		border-color: #1890ff;
		outline: none;
	}

	textarea.data-input {
		min-height: 100px;
		resize: vertical;
	}

	.char-count {
		font-size: 12px;
		color: #999;
		text-align: right;
		margin-top: 5px;
		display: block;
	}

	.picker-input {
		padding: 12px 15px;
		border: 1px solid #d9d9d9;
		border-radius: 8px;
		background: white;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.picker-arrow {
		color: #999;
		font-size: 16px;
	}

	/* 预览区域 */
	.preview-content {
		background: #f8f9fa;
		border-radius: 8px;
		padding: 15px;
	}

	.preview-text {
		font-size: 14px;
		color: #333;
		line-height: 1.6;
		word-break: break-all;
	}

	/* 扫描状态 */
	.scan-status {
		text-align: center;
		margin-bottom: 30px;
	}

	.scan-indicator {
		width: 100px;
		height: 100px;
		border-radius: 50px;
		background: #f0f0f0;
		margin: 0 auto 15px;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.scan-indicator.scanning {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		animation: pulse 2s infinite;
	}

	.scan-indicator.found {
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
	}

	@keyframes pulse {
		0% { transform: scale(1); }
		50% { transform: scale(1.1); }
		100% { transform: scale(1); }
	}

	.scan-icon {
		font-size: 35px;
		color: white;
	}

	.scan-text {
		font-size: 16px;
		color: #666;
		font-weight: bold;
	}

	/* 操作按钮 */
	.actions {
		margin-bottom: 30px;
	}

	.action-btn {
		width: 100%;
		height: 50px;
		border-radius: 12px;
		margin-bottom: 15px;
		border: none;
		font-size: 16px;
		font-weight: bold;
	}

	.action-btn:last-child {
		margin-bottom: 0;
	}

	.primary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}

	.secondary {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		color: white;
	}

	.action-btn[disabled] {
		background: #d9d9d9 !important;
		color: #999 !important;
	}

	/* 模板列表 */
	.template-list {
		display: flex;
		gap: 15px;
	}

	.template-item {
		flex: 1;
		height: 70px;
		border: 1px solid #f0f0f0;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #fafafa;
		transition: all 0.3s ease;
	}

	.template-item:active {
		background: #e6f7ff;
		border-color: #1890ff;
	}

	.template-icon {
		font-size: 24px;
		margin-bottom: 5px;
	}

	.template-label {
		font-size: 12px;
		color: #666;
	}

	/* 消息提示 */
	.error-message {
		background: #fff2f0;
		border: 1px solid #ffccc7;
		border-radius: 8px;
		padding: 15px;
		margin-top: 20px;
	}

	.error-text {
		font-size: 14px;
		color: #ff4d4f;
		line-height: 1.4;
	}

	.success-message {
		background: #f6ffed;
		border: 1px solid #b7eb8f;
		border-radius: 8px;
		padding: 15px;
		margin-top: 20px;
	}

	.success-text {
		font-size: 14px;
		color: #52c41a;
		line-height: 1.4;
	}
</style>

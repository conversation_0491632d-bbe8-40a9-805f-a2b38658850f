<template>
	<view class="container">
		<view class="header">
			<text class="page-title">NFC读取</text>
			<text class="page-subtitle">将设备靠近NFC标签进行读取</text>
		</view>

		<!-- 扫描状态 -->
		<view class="scan-status">
			<view class="scan-indicator" :class="{ 'scanning': isScanning, 'found': tagFound }">
				<text class="scan-icon">{{tagFound ? '✅' : (isScanning ? '📡' : '📱')}}</text>
			</view>
			<text class="scan-text">
				{{tagFound ? '已发现标签' : (isScanning ? '正在扫描...' : '点击开始扫描')}}
			</text>
		</view>

		<!-- 操作按钮 -->
		<view class="actions">
			<button class="action-btn primary" @click="toggleScan" :disabled="!nfcAvailable">
				{{isScanning ? '停止扫描' : '开始扫描'}}
			</button>
			
			<button class="action-btn secondary" @click="readNDEF" 
					:disabled="!tagFound || isReading" v-if="tagFound">
				{{isReading ? '读取中...' : '读取NDEF数据'}}
			</button>
		</view>

		<!-- 标签信息 -->
		<view class="tag-info" v-if="currentTag">
			<view class="info-section">
				<text class="section-title">标签信息</text>
				<view class="info-item">
					<text class="info-label">标签ID:</text>
					<text class="info-value">{{currentTag.tagId}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">技术类型:</text>
					<text class="info-value">{{currentTag.techs ? currentTag.techs.join(', ') : '未知'}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">发现时间:</text>
					<text class="info-value">{{formatDateTime(currentTag.timestamp)}}</text>
				</view>
			</view>
		</view>

		<!-- NDEF数据显示 -->
		<view class="ndef-data" v-if="ndefData">
			<view class="info-section">
				<text class="section-title">NDEF数据</text>
				<view class="data-content">
					<text class="data-text">{{formattedNdefData}}</text>
				</view>
				<view class="data-actions">
					<button class="mini-btn" @click="copyData">复制数据</button>
					<button class="mini-btn" @click="shareData">分享数据</button>
				</view>
			</view>
		</view>

		<!-- 原始数据显示 -->
		<view class="raw-data" v-if="currentTag && showRawData">
			<view class="info-section">
				<text class="section-title">原始数据</text>
				<view class="data-content">
					<text class="data-text raw">{{JSON.stringify(currentTag.rawData, null, 2)}}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="bottom-btn" @click="toggleRawData" v-if="currentTag">
				{{showRawData ? '隐藏' : '显示'}}原始数据
			</button>
			
			<button class="bottom-btn" @click="saveRecord" v-if="currentTag">
				保存记录
			</button>
			
			<button class="bottom-btn" @click="clearData">
				清除数据
			</button>
		</view>

		<!-- 错误提示 -->
		<view class="error-message" v-if="errorMessage">
			<text class="error-text">{{errorMessage}}</text>
		</view>
	</view>
</template>

<script>
	import nfcManager from '@/utils/nfc.js'
	
	export default {
		data() {
			return {
				isScanning: false,
				tagFound: false,
				nfcAvailable: false,
				currentTag: null,
				ndefData: null,
				isReading: false,
				showRawData: false,
				errorMessage: '',
				recordId: null // 用于显示特定记录
			}
		},
		computed: {
			formattedNdefData() {
				if (!this.ndefData) return '';
				return nfcManager.formatNFCData(this.ndefData);
			}
		},
		async onLoad(options) {
			// 检查是否是查看特定记录
			if (options.recordId) {
				this.recordId = options.recordId;
				this.loadRecord(options.recordId);
			}
			
			await this.checkNFCStatus();
			
			// 监听NFC标签发现
			uni.$on('nfc-tag-found', this.onTagFound);
		},
		onUnload() {
			// 清理事件监听
			uni.$off('nfc-tag-found', this.onTagFound);
			// 停止扫描
			if (this.isScanning) {
				nfcManager.stopDiscovery();
			}
		},
		methods: {
			async checkNFCStatus() {
				try {
					const status = await nfcManager.checkNFCAvailability();
					this.nfcAvailable = status.available;
					if (!status.available) {
						this.errorMessage = status.message;
					}
				} catch (error) {
					this.nfcAvailable = false;
					this.errorMessage = '检查NFC状态失败: ' + error.message;
				}
			},
			
			async toggleScan() {
				try {
					if (this.isScanning) {
						await nfcManager.stopDiscovery();
						this.isScanning = false;
						uni.showToast({ title: '已停止扫描', icon: 'success' });
					} else {
						await nfcManager.startDiscovery();
						this.isScanning = true;
						this.errorMessage = '';
						uni.showToast({ title: '开始扫描NFC', icon: 'success' });
					}
				} catch (error) {
					this.errorMessage = '操作失败: ' + error.message;
					console.error('切换扫描状态失败:', error);
				}
			},
			
			onTagFound(tag) {
				console.log('读取页面收到NFC标签:', tag);
				this.currentTag = tag;
				this.tagFound = true;
				this.errorMessage = '';
				
				// 自动尝试读取NDEF数据
				this.readNDEF();
			},
			
			async readNDEF() {
				if (!this.currentTag) return;
				
				this.isReading = true;
				this.errorMessage = '';
				
				try {
					const result = await nfcManager.readNDEF(this.currentTag.tagId);
					this.ndefData = result;
					uni.showToast({ title: '读取成功', icon: 'success' });
				} catch (error) {
					this.errorMessage = '读取NDEF失败: ' + error.message;
					console.error('读取NDEF失败:', error);
				} finally {
					this.isReading = false;
				}
			},
			
			loadRecord(recordId) {
				const records = nfcManager.getRecords();
				const record = records.find(r => r.id === recordId);
				if (record) {
					this.currentTag = record;
					this.tagFound = true;
					if (record.messages) {
						this.ndefData = { messages: record.messages };
					}
				}
			},
			
			copyData() {
				if (!this.formattedNdefData) return;
				
				uni.setClipboardData({
					data: this.formattedNdefData,
					success: () => {
						uni.showToast({ title: '已复制到剪贴板', icon: 'success' });
					}
				});
			},
			
			shareData() {
				if (!this.formattedNdefData) return;
				
				uni.share({
					provider: 'weixin',
					type: 0,
					title: 'NFC数据分享',
					summary: this.formattedNdefData,
					success: () => {
						uni.showToast({ title: '分享成功', icon: 'success' });
					},
					fail: (error) => {
						console.error('分享失败:', error);
						uni.showToast({ title: '分享失败', icon: 'error' });
					}
				});
			},
			
			toggleRawData() {
				this.showRawData = !this.showRawData;
			},
			
			saveRecord() {
				if (!this.currentTag) return;
				
				uni.showToast({ title: '记录已保存', icon: 'success' });
			},
			
			clearData() {
				this.currentTag = null;
				this.ndefData = null;
				this.tagFound = false;
				this.showRawData = false;
				this.errorMessage = '';
				uni.showToast({ title: '数据已清除', icon: 'success' });
			},
			
			formatDateTime(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
			}
		}
	}
</script>

<style>
	.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
	}

	.header {
		text-align: center;
		margin-bottom: 30px;
	}

	.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8px;
	}

	.page-subtitle {
		font-size: 14px;
		color: #666;
	}

	/* 扫描状态 */
	.scan-status {
		text-align: center;
		margin-bottom: 30px;
	}

	.scan-indicator {
		width: 120px;
		height: 120px;
		border-radius: 60px;
		background: #f0f0f0;
		margin: 0 auto 15px;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.scan-indicator.scanning {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		animation: pulse 2s infinite;
	}

	.scan-indicator.found {
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
	}

	@keyframes pulse {
		0% { transform: scale(1); }
		50% { transform: scale(1.1); }
		100% { transform: scale(1); }
	}

	.scan-icon {
		font-size: 40px;
		color: white;
	}

	.scan-text {
		font-size: 16px;
		color: #666;
		font-weight: bold;
	}

	/* 操作按钮 */
	.actions {
		margin-bottom: 30px;
	}

	.action-btn {
		width: 100%;
		height: 50px;
		border-radius: 12px;
		margin-bottom: 15px;
		border: none;
		font-size: 16px;
		font-weight: bold;
	}

	.action-btn:last-child {
		margin-bottom: 0;
	}

	.primary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}

	.secondary {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		color: white;
	}

	.action-btn[disabled] {
		background: #d9d9d9 !important;
		color: #999 !important;
	}

	/* 信息区域 */
	.tag-info, .ndef-data, .raw-data {
		margin-bottom: 20px;
	}

	.info-section {
		background: white;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	}

	.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
		display: block;
	}

	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 12px;
		padding-bottom: 12px;
		border-bottom: 1px solid #f0f0f0;
	}

	.info-item:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
	}

	.info-label {
		font-size: 14px;
		color: #666;
		flex-shrink: 0;
		margin-right: 15px;
	}

	.info-value {
		font-size: 14px;
		color: #333;
		text-align: right;
		word-break: break-all;
	}

	/* 数据内容 */
	.data-content {
		background: #f8f9fa;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 15px;
	}

	.data-text {
		font-size: 14px;
		color: #333;
		line-height: 1.6;
		word-break: break-all;
	}

	.data-text.raw {
		font-family: monospace;
		font-size: 12px;
		color: #666;
	}

	.data-actions {
		display: flex;
		gap: 10px;
	}

	.mini-btn {
		flex: 1;
		height: 35px;
		border-radius: 6px;
		border: 1px solid #d9d9d9;
		background: white;
		font-size: 14px;
		color: #666;
	}

	/* 底部操作 */
	.bottom-actions {
		display: flex;
		flex-wrap: wrap;
		gap: 10px;
		margin-top: 30px;
	}

	.bottom-btn {
		flex: 1;
		min-width: 100px;
		height: 40px;
		border-radius: 8px;
		border: 1px solid #d9d9d9;
		background: white;
		font-size: 14px;
		color: #666;
	}

	/* 错误提示 */
	.error-message {
		background: #fff2f0;
		border: 1px solid #ffccc7;
		border-radius: 8px;
		padding: 15px;
		margin-top: 20px;
	}

	.error-text {
		font-size: 14px;
		color: #ff4d4f;
		line-height: 1.4;
	}
</style>

<template>
	<view class="container">
		<image class="logo" src="/static/logo.png"></image>
		<view class="text-area">
			<text class="title">{{title}}</text>
		</view>

		<!-- NFC状态显示 -->
		<view class="nfc-status">
			<view class="status-item">
				<text class="status-label">NFC状态:</text>
				<text class="status-value" :class="nfcStatus.available ? 'status-success' : 'status-error'">
					{{nfcStatus.message}}
				</text>
			</view>
			<view class="status-item" v-if="nfcStatus.available">
				<text class="status-label">发现状态:</text>
				<text class="status-value" :class="isDiscovering ? 'status-success' : 'status-normal'">
					{{isDiscovering ? '正在扫描' : '未扫描'}}
				</text>
			</view>
		</view>

		<!-- NFC功能按钮 -->
		<view class="nfc-actions">
			<button class="action-btn primary" @click="goToRead" :disabled="!nfcStatus.available">
				<text class="btn-icon">📖</text>
				<text class="btn-text">读取NFC</text>
			</button>

			<button class="action-btn secondary" @click="goToWrite" :disabled="!nfcStatus.available">
				<text class="btn-icon">✏️</text>
				<text class="btn-text">写入NFC</text>
			</button>

			<button class="action-btn tertiary" @click="goToRecords">
				<text class="btn-icon">📋</text>
				<text class="btn-text">操作记录</text>
			</button>
		</view>

		<!-- 快速操作 -->
		<view class="quick-actions" v-if="nfcStatus.available">
			<button class="quick-btn" @click="toggleDiscovery"
					:class="isDiscovering ? 'stop-btn' : 'start-btn'">
				{{isDiscovering ? '停止扫描' : '开始扫描'}}
			</button>

			<button class="quick-btn refresh-btn" @click="refreshNFCStatus">
				刷新状态
			</button>

			<button class="quick-btn test-btn" @click="goToTest">
				功能测试
			</button>
		</view>

		<!-- 最近记录 -->
		<view class="recent-records" v-if="recentRecords.length > 0">
			<view class="section-title">最近操作</view>
			<view class="record-item" v-for="record in recentRecords" :key="record.id" @click="viewRecord(record)">
				<view class="record-header">
					<text class="record-type" :class="record.type">{{record.type === 'read' ? '读取' : '写入'}}</text>
					<text class="record-time">{{formatTime(record.timestamp)}}</text>
				</view>
				<text class="record-content">{{getRecordPreview(record)}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import nfcManager from '@/utils/nfc.js'

	export default {
		data() {
			return {
				title: 'NFC管理工具',
				nfcStatus: {
					available: false,
					message: '检查中...'
				},
				isDiscovering: false,
				recentRecords: []
			}
		},
		async onLoad() {
			await this.initNFC();
			this.loadRecentRecords();

			// 监听NFC标签发现事件
			uni.$on('nfc-tag-found', this.onTagFound);
		},
		onUnload() {
			// 清理事件监听
			uni.$off('nfc-tag-found', this.onTagFound);
			// 停止NFC发现
			if (this.isDiscovering) {
				nfcManager.stopDiscovery();
			}
		},
		methods: {
			async initNFC() {
				try {
					const status = await nfcManager.checkNFCAvailability();
					this.nfcStatus = status;
					this.isDiscovering = status.discovering || false;
				} catch (error) {
					console.error('初始化NFC失败:', error);
					this.nfcStatus = {
						available: false,
						message: '初始化失败: ' + error.message
					};
				}
			},

			async refreshNFCStatus() {
				uni.showLoading({ title: '检查中...' });
				await this.initNFC();
				uni.hideLoading();
				uni.showToast({
					title: this.nfcStatus.available ? 'NFC可用' : 'NFC不可用',
					icon: this.nfcStatus.available ? 'success' : 'error'
				});
			},

			async toggleDiscovery() {
				try {
					if (this.isDiscovering) {
						await nfcManager.stopDiscovery();
						this.isDiscovering = false;
						uni.showToast({ title: '已停止扫描', icon: 'success' });
					} else {
						await nfcManager.startDiscovery();
						this.isDiscovering = true;
						uni.showToast({ title: '开始扫描NFC', icon: 'success' });
					}
				} catch (error) {
					console.error('切换发现状态失败:', error);
					uni.showToast({
						title: '操作失败: ' + error.message,
						icon: 'error'
					});
				}
			},

			goToRead() {
				uni.navigateTo({
					url: '/pages/nfc-read/nfc-read'
				});
			},

			goToWrite() {
				uni.navigateTo({
					url: '/pages/nfc-write/nfc-write'
				});
			},

			goToRecords() {
				uni.navigateTo({
					url: '/pages/nfc-records/nfc-records'
				});
			},

			goToTest() {
				uni.navigateTo({
					url: '/pages/nfc-test/nfc-test'
				});
			},

			loadRecentRecords() {
				this.recentRecords = nfcManager.getRecords(3);
			},

			onTagFound(record) {
				console.log('主页面收到NFC标签:', record);
				this.loadRecentRecords();

				// 显示发现提示
				uni.showModal({
					title: '发现NFC标签',
					content: `标签ID: ${record.tagId}\n是否查看详情？`,
					success: (res) => {
						if (res.confirm) {
							this.viewRecord(record);
						}
					}
				});
			},

			viewRecord(record) {
				uni.navigateTo({
					url: `/pages/nfc-read/nfc-read?recordId=${record.id}`
				});
			},

			formatTime(timestamp) {
				const date = new Date(timestamp);
				return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
			},

			getRecordPreview(record) {
				if (record.error) {
					return `错误: ${record.error}`;
				}

				if (record.type === 'write') {
					return `写入: ${record.data ? record.data.substring(0, 20) + '...' : '无数据'}`;
				}

				if (record.messages && record.messages.length > 0) {
					const preview = nfcManager.formatNFCData(record.messages[0]);
					return preview.length > 30 ? preview.substring(0, 30) + '...' : preview;
				}

				return '无数据';
			}
		}
	}
</script>

<style>
	.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
	}

	.logo {
		height: 80px;
		width: 80px;
		margin: 20px auto 15px auto;
		display: block;
	}

	.title {
		font-size: 24px;
		color: #333;
		text-align: center;
		font-weight: bold;
		margin-bottom: 30px;
	}

	/* NFC状态显示 */
	.nfc-status {
		background: white;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	}

	.status-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}

	.status-item:last-child {
		margin-bottom: 0;
	}

	.status-label {
		font-size: 16px;
		color: #666;
	}

	.status-value {
		font-size: 16px;
		font-weight: bold;
	}

	.status-success {
		color: #52c41a;
	}

	.status-error {
		color: #ff4d4f;
	}

	.status-normal {
		color: #1890ff;
	}

	/* 功能按钮 */
	.nfc-actions {
		margin-bottom: 20px;
	}

	.action-btn {
		width: 100%;
		height: 60px;
		border-radius: 12px;
		margin-bottom: 15px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: none;
		font-size: 16px;
		font-weight: bold;
	}

	.action-btn:last-child {
		margin-bottom: 0;
	}

	.primary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}

	.secondary {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		color: white;
	}

	.tertiary {
		background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
		color: white;
	}

	.action-btn[disabled] {
		background: #d9d9d9 !important;
		color: #999 !important;
	}

	.btn-icon {
		font-size: 20px;
		margin-right: 10px;
	}

	.btn-text {
		font-size: 16px;
	}

	/* 快速操作 */
	.quick-actions {
		display: flex;
		gap: 10px;
		margin-bottom: 20px;
	}

	.quick-btn {
		flex: 1;
		height: 40px;
		border-radius: 8px;
		border: none;
		font-size: 14px;
		font-weight: bold;
	}

	.start-btn {
		background: #52c41a;
		color: white;
	}

	.stop-btn {
		background: #ff4d4f;
		color: white;
	}

	.refresh-btn {
		background: #1890ff;
		color: white;
	}

	.test-btn {
		background: #52c41a;
		color: white;
	}

	/* 最近记录 */
	.recent-records {
		background: white;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	}

	.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
	}

	.record-item {
		padding: 15px;
		border: 1px solid #f0f0f0;
		border-radius: 8px;
		margin-bottom: 10px;
		background: #fafafa;
	}

	.record-item:last-child {
		margin-bottom: 0;
	}

	.record-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8px;
	}

	.record-type {
		font-size: 12px;
		padding: 2px 8px;
		border-radius: 4px;
		color: white;
		font-weight: bold;
	}

	.record-type.read {
		background: #52c41a;
	}

	.record-type.write {
		background: #1890ff;
	}

	.record-time {
		font-size: 12px;
		color: #999;
	}

	.record-content {
		font-size: 14px;
		color: #666;
		line-height: 1.4;
	}
</style>

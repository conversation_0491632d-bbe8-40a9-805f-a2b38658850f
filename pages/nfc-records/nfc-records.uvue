<template>
	<view class="container">
		<view class="header">
			<text class="page-title">操作记录</text>
			<text class="page-subtitle">管理NFC读取和写入历史记录</text>
		</view>

		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stat-item">
				<text class="stat-number">{{totalRecords}}</text>
				<text class="stat-label">总记录</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{readRecords}}</text>
				<text class="stat-label">读取</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{writeRecords}}</text>
				<text class="stat-label">写入</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{successRate}}%</text>
				<text class="stat-label">成功率</text>
			</view>
		</view>

		<!-- 筛选和操作 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view class="filter-tab" 
					  :class="{ 'active': currentFilter === 'all' }" 
					  @click="setFilter('all')">
					全部
				</view>
				<view class="filter-tab" 
					  :class="{ 'active': currentFilter === 'read' }" 
					  @click="setFilter('read')">
					读取
				</view>
				<view class="filter-tab" 
					  :class="{ 'active': currentFilter === 'write' }" 
					  @click="setFilter('write')">
					写入
				</view>
				<view class="filter-tab" 
					  :class="{ 'active': currentFilter === 'error' }" 
					  @click="setFilter('error')">
					错误
				</view>
			</view>
			
			<view class="action-buttons">
				<button class="action-btn export" @click="exportRecords">
					<text class="btn-icon">📤</text>
					<text class="btn-text">导出</text>
				</button>
				<button class="action-btn clear" @click="confirmClearRecords">
					<text class="btn-icon">🗑️</text>
					<text class="btn-text">清空</text>
				</button>
			</view>
		</view>

		<!-- 记录列表 -->
		<view class="records-list" v-if="filteredRecords.length > 0">
			<view class="record-item" 
				  v-for="record in filteredRecords" 
				  :key="record.id"
				  @click="viewRecord(record)"
				  @longpress="showRecordActions(record)">
				
				<view class="record-header">
					<view class="record-type-badge" :class="record.type">
						<text class="badge-text">{{record.type === 'read' ? '读取' : '写入'}}</text>
					</view>
					<view class="record-status" :class="record.success ? 'success' : 'error'">
						<text class="status-icon">{{record.success ? '✅' : '❌'}}</text>
					</view>
				</view>

				<view class="record-content">
					<text class="record-preview">{{getRecordPreview(record)}}</text>
					<text class="record-tag-id" v-if="record.tagId">标签ID: {{record.tagId}}</text>
				</view>

				<view class="record-footer">
					<text class="record-time">{{formatDateTime(record.timestamp)}}</text>
					<view class="record-actions">
						<text class="action-icon" @click.stop="shareRecord(record)">📤</text>
						<text class="action-icon" @click.stop="deleteRecord(record)">🗑️</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<text class="empty-icon">📋</text>
			<text class="empty-title">暂无记录</text>
			<text class="empty-subtitle">{{getEmptyMessage()}}</text>
			<button class="empty-action" @click="goToRead">开始使用NFC</button>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore && filteredRecords.length > 0">
			<button class="load-more-btn" @click="loadMore" :disabled="isLoading">
				{{isLoading ? '加载中...' : '加载更多'}}
			</button>
		</view>
	</view>
</template>

<script>
	import nfcManager from '@/utils/nfc.js'
	
	export default {
		data() {
			return {
				records: [],
				currentFilter: 'all',
				isLoading: false,
				hasMore: false,
				pageSize: 20,
				currentPage: 1
			}
		},
		computed: {
			filteredRecords() {
				let filtered = this.records;
				
				switch (this.currentFilter) {
					case 'read':
						filtered = this.records.filter(r => r.type === 'read');
						break;
					case 'write':
						filtered = this.records.filter(r => r.type === 'write');
						break;
					case 'error':
						filtered = this.records.filter(r => !r.success);
						break;
				}
				
				return filtered;
			},
			totalRecords() {
				return this.records.length;
			},
			readRecords() {
				return this.records.filter(r => r.type === 'read').length;
			},
			writeRecords() {
				return this.records.filter(r => r.type === 'write').length;
			},
			successRate() {
				if (this.totalRecords === 0) return 0;
				const successCount = this.records.filter(r => r.success).length;
				return Math.round((successCount / this.totalRecords) * 100);
			}
		},
		onLoad() {
			this.loadRecords();
		},
		onShow() {
			// 页面显示时刷新记录
			this.loadRecords();
		},
		methods: {
			loadRecords() {
				this.records = nfcManager.getRecords();
				this.hasMore = false; // 目前简单实现，不分页
			},
			
			setFilter(filter) {
				this.currentFilter = filter;
			},
			
			viewRecord(record) {
				// 根据记录类型跳转到相应页面
				if (record.type === 'read') {
					uni.navigateTo({
						url: `/pages/nfc-read/nfc-read?recordId=${record.id}`
					});
				} else {
					// 显示记录详情
					this.showRecordDetail(record);
				}
			},
			
			showRecordDetail(record) {
				const content = this.getRecordDetail(record);
				uni.showModal({
					title: '记录详情',
					content: content,
					showCancel: false,
					confirmText: '确定'
				});
			},
			
			showRecordActions(record) {
				uni.showActionSheet({
					itemList: ['查看详情', '分享记录', '删除记录'],
					success: (res) => {
						switch (res.tapIndex) {
							case 0:
								this.showRecordDetail(record);
								break;
							case 1:
								this.shareRecord(record);
								break;
							case 2:
								this.deleteRecord(record);
								break;
						}
					}
				});
			},
			
			shareRecord(record) {
				const content = this.getRecordDetail(record);
				uni.share({
					provider: 'weixin',
					type: 0,
					title: 'NFC操作记录',
					summary: content,
					success: () => {
						uni.showToast({ title: '分享成功', icon: 'success' });
					},
					fail: (error) => {
						console.error('分享失败:', error);
						// 备用方案：复制到剪贴板
						uni.setClipboardData({
							data: content,
							success: () => {
								uni.showToast({ title: '已复制到剪贴板', icon: 'success' });
							}
						});
					}
				});
			},
			
			deleteRecord(record) {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这条记录吗？',
					success: (res) => {
						if (res.confirm) {
							const success = nfcManager.deleteRecord(record.id);
							if (success) {
								this.loadRecords();
								uni.showToast({ title: '删除成功', icon: 'success' });
							} else {
								uni.showToast({ title: '删除失败', icon: 'error' });
							}
						}
					}
				});
			},
			
			exportRecords() {
				try {
					const data = nfcManager.exportRecords();
					uni.setClipboardData({
						data: data,
						success: () => {
							uni.showToast({ title: '记录已复制到剪贴板', icon: 'success' });
						}
					});
				} catch (error) {
					uni.showToast({ title: '导出失败', icon: 'error' });
				}
			},
			
			confirmClearRecords() {
				uni.showModal({
					title: '确认清空',
					content: '确定要清空所有记录吗？此操作不可恢复。',
					success: (res) => {
						if (res.confirm) {
							nfcManager.clearRecords();
							this.loadRecords();
							uni.showToast({ title: '记录已清空', icon: 'success' });
						}
					}
				});
			},
			
			loadMore() {
				// 预留分页加载功能
				this.isLoading = true;
				setTimeout(() => {
					this.isLoading = false;
				}, 1000);
			},
			
			goToRead() {
				uni.navigateTo({
					url: '/pages/nfc-read/nfc-read'
				});
			},
			
			getRecordPreview(record) {
				if (record.error) {
					return `错误: ${record.error}`;
				}
				
				if (record.type === 'write') {
					return `写入: ${record.data ? record.data.substring(0, 30) + '...' : '无数据'}`;
				}
				
				if (record.messages && record.messages.length > 0) {
					const preview = nfcManager.formatNFCData(record.messages[0]);
					return preview.length > 50 ? preview.substring(0, 50) + '...' : preview;
				}
				
				return '无数据';
			},
			
			getRecordDetail(record) {
				let detail = `类型: ${record.type === 'read' ? '读取' : '写入'}\n`;
				detail += `时间: ${this.formatDateTime(record.timestamp)}\n`;
				detail += `状态: ${record.success ? '成功' : '失败'}\n`;
				
				if (record.tagId) {
					detail += `标签ID: ${record.tagId}\n`;
				}
				
				if (record.error) {
					detail += `错误: ${record.error}\n`;
				} else if (record.data) {
					detail += `数据: ${record.data}\n`;
				} else if (record.messages) {
					detail += `数据: ${nfcManager.formatNFCData(record.messages)}\n`;
				}
				
				return detail;
			},
			
			getEmptyMessage() {
				switch (this.currentFilter) {
					case 'read':
						return '还没有读取记录';
					case 'write':
						return '还没有写入记录';
					case 'error':
						return '没有错误记录';
					default:
						return '开始使用NFC功能来创建记录';
				}
			},
			
			formatDateTime(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
			}
		}
	}
</script>

<style>
	.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
	}

	.header {
		text-align: center;
		margin-bottom: 30px;
	}

	.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8px;
	}

	.page-subtitle {
		font-size: 14px;
		color: #666;
	}

	/* 统计信息 */
	.stats-section {
		display: flex;
		background: white;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	}

	.stat-item {
		flex: 1;
		text-align: center;
	}

	.stat-number {
		font-size: 24px;
		font-weight: bold;
		color: #1890ff;
		display: block;
		margin-bottom: 5px;
	}

	.stat-label {
		font-size: 12px;
		color: #666;
	}

	/* 筛选区域 */
	.filter-section {
		background: white;
		border-radius: 12px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	}

	.filter-tabs {
		display: flex;
		gap: 10px;
		margin-bottom: 15px;
	}

	.filter-tab {
		flex: 1;
		height: 35px;
		border: 1px solid #d9d9d9;
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		color: #666;
		background: white;
		transition: all 0.3s ease;
	}

	.filter-tab.active {
		background: #1890ff;
		color: white;
		border-color: #1890ff;
	}

	.action-buttons {
		display: flex;
		gap: 10px;
	}

	.action-btn {
		flex: 1;
		height: 40px;
		border-radius: 8px;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 5px;
		font-size: 14px;
		font-weight: bold;
	}

	.export {
		background: #52c41a;
		color: white;
	}

	.clear {
		background: #ff4d4f;
		color: white;
	}

	.btn-icon {
		font-size: 16px;
	}

	.btn-text {
		font-size: 14px;
	}

	/* 记录列表 */
	.records-list {
		margin-bottom: 20px;
	}

	.record-item {
		background: white;
		border-radius: 12px;
		padding: 15px;
		margin-bottom: 15px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		transition: all 0.3s ease;
	}

	.record-item:active {
		transform: scale(0.98);
	}

	.record-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}

	.record-type-badge {
		padding: 4px 8px;
		border-radius: 4px;
		font-size: 12px;
		font-weight: bold;
	}

	.record-type-badge.read {
		background: #e6f7ff;
		color: #1890ff;
	}

	.record-type-badge.write {
		background: #f6ffed;
		color: #52c41a;
	}

	.badge-text {
		font-size: 12px;
	}

	.record-status {
		display: flex;
		align-items: center;
	}

	.status-icon {
		font-size: 16px;
	}

	.record-content {
		margin-bottom: 10px;
	}

	.record-preview {
		font-size: 14px;
		color: #333;
		line-height: 1.4;
		display: block;
		margin-bottom: 5px;
	}

	.record-tag-id {
		font-size: 12px;
		color: #999;
	}

	.record-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.record-time {
		font-size: 12px;
		color: #999;
	}

	.record-actions {
		display: flex;
		gap: 10px;
	}

	.action-icon {
		font-size: 16px;
		padding: 5px;
		border-radius: 4px;
		background: #f0f0f0;
		transition: all 0.3s ease;
	}

	.action-icon:active {
		background: #d9d9d9;
	}

	/* 空状态 */
	.empty-state {
		text-align: center;
		padding: 60px 20px;
		background: white;
		border-radius: 12px;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	}

	.empty-icon {
		font-size: 60px;
		display: block;
		margin-bottom: 20px;
		opacity: 0.5;
	}

	.empty-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 10px;
	}

	.empty-subtitle {
		font-size: 14px;
		color: #666;
		display: block;
		margin-bottom: 30px;
	}

	.empty-action {
		width: 150px;
		height: 40px;
		background: #1890ff;
		color: white;
		border: none;
		border-radius: 8px;
		font-size: 14px;
		font-weight: bold;
	}

	/* 加载更多 */
	.load-more {
		text-align: center;
		padding: 20px;
	}

	.load-more-btn {
		width: 150px;
		height: 40px;
		background: white;
		border: 1px solid #d9d9d9;
		border-radius: 8px;
		font-size: 14px;
		color: #666;
	}

	.load-more-btn[disabled] {
		background: #f5f5f5;
		color: #999;
	}
</style>

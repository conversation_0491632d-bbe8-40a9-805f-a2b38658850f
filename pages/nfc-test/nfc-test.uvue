<template>
	<view class="container">
		<view class="header">
			<text class="page-title">NFC功能测试</text>
			<text class="page-subtitle">测试NFC API是否正常工作</text>
		</view>

		<!-- 测试结果 -->
		<view class="test-results">
			<view class="test-item" v-for="(test, index) in testResults" :key="index">
				<view class="test-header">
					<text class="test-name">{{test.name}}</text>
					<text class="test-status" :class="test.status">
						{{test.status === 'success' ? '✅' : test.status === 'error' ? '❌' : '⏳'}}
					</text>
				</view>
				<text class="test-message">{{test.message}}</text>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="actions">
			<button class="action-btn primary" @click="runTests" :disabled="isRunning">
				{{isRunning ? '测试中...' : '开始测试'}}
			</button>
			<button class="action-btn secondary" @click="clearResults">
				清除结果
			</button>
		</view>

		<!-- 详细日志 -->
		<view class="logs" v-if="logs.length > 0">
			<view class="section-title">详细日志</view>
			<view class="log-item" v-for="(log, index) in logs" :key="index">
				<text class="log-time">{{formatTime(log.time)}}</text>
				<text class="log-message">{{log.message}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import nfcManager from '@/utils/nfc.js'
	
	export default {
		data() {
			return {
				testResults: [],
				logs: [],
				isRunning: false
			}
		},
		onLoad() {
			this.addLog('页面加载完成');
		},
		methods: {
			async runTests() {
				this.isRunning = true;
				this.testResults = [];
				this.logs = [];
				this.addLog('开始运行NFC功能测试');

				// 测试1: NFC适配器初始化
				await this.testNFCInit();
				
				// 测试2: 检查NFC可用性
				await this.testNFCAvailability();
				
				// 测试3: 测试发现功能
				await this.testDiscovery();
				
				// 测试4: 测试记录管理
				await this.testRecordManagement();

				this.isRunning = false;
				this.addLog('所有测试完成');
			},

			async testNFCInit() {
				const testName = 'NFC适配器初始化';
				this.addLog(`开始测试: ${testName}`);
				
				try {
					// 检查nfcManager是否正确初始化
					if (nfcManager && nfcManager.nfcAdapter) {
						this.addTestResult(testName, 'success', 'NFC适配器初始化成功');
						this.addLog('✅ NFC适配器已正确初始化');
					} else {
						this.addTestResult(testName, 'error', 'NFC适配器未初始化');
						this.addLog('❌ NFC适配器初始化失败');
					}
				} catch (error) {
					this.addTestResult(testName, 'error', `初始化失败: ${error.message}`);
					this.addLog(`❌ 初始化异常: ${error.message}`);
				}
			},

			async testNFCAvailability() {
				const testName = 'NFC可用性检查';
				this.addLog(`开始测试: ${testName}`);
				
				try {
					const status = await nfcManager.checkNFCAvailability();
					this.addTestResult(testName, status.available ? 'success' : 'error', status.message);
					this.addLog(`📱 NFC状态: ${status.message}`);
				} catch (error) {
					this.addTestResult(testName, 'error', `检查失败: ${error.message}`);
					this.addLog(`❌ 检查异常: ${error.message}`);
				}
			},

			async testDiscovery() {
				const testName = 'NFC发现功能';
				this.addLog(`开始测试: ${testName}`);
				
				try {
					// 测试开始发现
					await nfcManager.startDiscovery();
					this.addLog('✅ 开始NFC发现成功');
					
					// 等待2秒
					await new Promise(resolve => setTimeout(resolve, 2000));
					
					// 测试停止发现
					await nfcManager.stopDiscovery();
					this.addLog('✅ 停止NFC发现成功');
					
					this.addTestResult(testName, 'success', 'NFC发现功能正常');
				} catch (error) {
					this.addTestResult(testName, 'error', `发现功能失败: ${error.message}`);
					this.addLog(`❌ 发现功能异常: ${error.message}`);
				}
			},

			async testRecordManagement() {
				const testName = '记录管理功能';
				this.addLog(`开始测试: ${testName}`);
				
				try {
					// 清除现有记录
					nfcManager.clearRecords();
					this.addLog('🗑️ 清除现有记录');
					
					// 模拟添加一条记录
					nfcManager.records.push({
						id: 'test-' + Date.now(),
						type: 'read',
						timestamp: new Date(),
						tagId: 'test-tag',
						data: '测试数据',
						success: true
					});
					
					// 获取记录
					const records = nfcManager.getRecords();
					if (records.length > 0) {
						this.addLog(`📋 获取到 ${records.length} 条记录`);
						
						// 测试导出
						const exportData = nfcManager.exportRecords();
						if (exportData && exportData.length > 0) {
							this.addLog('📤 记录导出成功');
							this.addTestResult(testName, 'success', '记录管理功能正常');
						} else {
							this.addTestResult(testName, 'error', '记录导出失败');
						}
					} else {
						this.addTestResult(testName, 'error', '无法获取记录');
					}
				} catch (error) {
					this.addTestResult(testName, 'error', `记录管理失败: ${error.message}`);
					this.addLog(`❌ 记录管理异常: ${error.message}`);
				}
			},

			addTestResult(name, status, message) {
				this.testResults.push({
					name,
					status,
					message
				});
			},

			addLog(message) {
				this.logs.push({
					time: new Date(),
					message
				});
				console.log(`[NFC测试] ${message}`);
			},

			clearResults() {
				this.testResults = [];
				this.logs = [];
				this.addLog('清除测试结果');
			},

			formatTime(time) {
				return time.toLocaleTimeString();
			}
		}
	}
</script>

<style>
	.container {
		padding: 20px;
		min-height: 100vh;
		background-color: #f8f8f8;
	}

	.header {
		text-align: center;
		margin-bottom: 30px;
	}

	.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8px;
	}

	.page-subtitle {
		font-size: 14px;
		color: #666;
	}

	.test-results {
		margin-bottom: 30px;
	}

	.test-item {
		background: white;
		border-radius: 8px;
		padding: 15px;
		margin-bottom: 10px;
		box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	}

	.test-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 5px;
	}

	.test-name {
		font-size: 16px;
		font-weight: bold;
		color: #333;
	}

	.test-status {
		font-size: 18px;
	}

	.test-message {
		font-size: 14px;
		color: #666;
		line-height: 1.4;
	}

	.actions {
		margin-bottom: 30px;
	}

	.action-btn {
		width: 100%;
		height: 50px;
		border-radius: 8px;
		margin-bottom: 15px;
		border: none;
		font-size: 16px;
		font-weight: bold;
	}

	.primary {
		background: #1890ff;
		color: white;
	}

	.secondary {
		background: #f5f5f5;
		color: #333;
	}

	.action-btn[disabled] {
		background: #d9d9d9 !important;
		color: #999 !important;
	}

	.logs {
		background: white;
		border-radius: 8px;
		padding: 15px;
		box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	}

	.section-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
		display: block;
	}

	.log-item {
		display: flex;
		margin-bottom: 8px;
		font-size: 12px;
	}

	.log-time {
		color: #999;
		margin-right: 10px;
		min-width: 80px;
	}

	.log-message {
		color: #333;
		flex: 1;
	}
</style>
